/* eslint-disable complexity */
import { useRef, useState } from 'react'
import {
  fileUploadVariants,
  type FileUploadProps,
} from '@thrift/design-system/packages/molecules/FileUpload'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const FileUpload: React.FC<FileUploadProps> = ({
  label = '',
  chooseFileText = '',
  noFileText = '',
  labelRemove = '',
  labelSelectedFiles = '',
  maxFiles = 1,
  accept = '*',
  errorMessage,
  disabled = false,
  onChange,
}) => {
  const inputRef = useRef<HTMLInputElement>(null)
  const [files, setFiles] = useState<File[]>([])

  const hasError = Boolean(errorMessage)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return
    const selected = e.target.files ? Array.from(e.target.files) : []
    const limited = selected.slice(0, maxFiles)

    setFiles(limited)
    onChange?.(limited)
  }

  const removeFile = (index: number) => {
    if (disabled) return
    const updated = files.filter((_, i) => i !== index)

    setFiles(updated)
    onChange?.(updated)
  }

  return (
    <div className="flex flex-col gap-2 w-full">
      <Typography weight="bold">{label}</Typography>

      <div className="flex items-center flex-nowrap gap-2 !px-tiny border border-gray-400 !rounded-medium w-full h-[4rem]">
        <button
          type="button"
          onClick={() => !disabled && inputRef.current?.click()}
          className={fileUploadVariants({ disabled })}
          disabled={disabled}
        >
          <Typography>{chooseFileText}</Typography>
        </button>
        <div className="w-px h-full bg-gray-400" />
        <div className="!px-tiny whitespace-nowrap overflow-hidden text-ellipsis">
          <Typography>
            {files.length > 0
              ? `${files.length} ${labelSelectedFiles}`
              : noFileText}
          </Typography>
        </div>
      </div>

      <input
        ref={inputRef}
        type="file"
        accept={accept}
        multiple={maxFiles > 1}
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled}
      />

      {files.length > 0 && (
        <ul className="mt-2 space-y-1">
          {files.map((file, index) => (
            <li
              key={`${file}-${index}`}
              className="flex items-center justify-between gap-2"
            >
              <Typography>{file.name}</Typography>
              <button
                onClick={() => removeFile(index)}
                disabled={disabled}
                className={'text-red hover:underline text-xs'}
              >
                <Typography className="text-red">{labelRemove}</Typography>
              </button>
            </li>
          ))}
        </ul>
      )}

      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
