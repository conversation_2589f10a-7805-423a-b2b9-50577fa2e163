import { tv } from 'tailwind-variants'

export const fileUploadVariants = tv({
  base: 'whitespace-nowrap !px-tiny py-2 transition',
  variants: {
    disabled: {
      true: '!bg-gray-100 !border-gray-200 !text-gray-400 cursor-not-allowed !hover:bg-gray-100',
    },
  },
  compoundVariants: [
    {
      error: true,
      disabled: true,
      className: '!text-red !bg-gray-100 !border-red-300 cursor-not-allowed',
    },
  ],
  defaultVariants: {
    disabled: false,
  },
})
