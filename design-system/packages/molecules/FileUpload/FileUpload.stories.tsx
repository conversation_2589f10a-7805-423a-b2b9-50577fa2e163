import type { Meta, StoryObj } from '@storybook/react-vite'
import { FileUpload } from '@thrift/design-system/packages/molecules/FileUpload'
import { useState } from 'react'

export default {
  title: 'Molecules/FileUpload',
  component: FileUpload,
} satisfies Meta<typeof FileUpload>

type Story = StoryObj<typeof FileUpload>

const Template = (args: React.ComponentProps<typeof FileUpload>) => {
  const [, setFiles] = useState<File[]>([])

  return (
    <div className="max-[6rem]">
      <FileUpload {...args} onChange={setFiles} />
    </div>
  )
}

export const Default: Story = {
  render: Template,
  args: {
    label: 'Documento',
    chooseFileText: 'Selecionar arquivo',
    noFileText: 'Nenhum arquivo selecionado',
    maxFiles: 1,
    accept: '*',
  },
}

export const WithError: Story = {
  render: Template,
  args: {
    label: 'Comprovante',
    chooseFileText: 'Enviar',
    noFileText: 'Nenhum arquivo enviado',
    errorMessage: 'O envio do arquivo é obrigatório',
    maxFiles: 1,
  },
}

export const Disabled: Story = {
  render: Template,
  args: {
    label: 'Arquivo bloqueado',
    chooseFileText: 'Selecionar',
    noFileText: 'Indisponível',
    disabled: true,
    maxFiles: 1,
  },
}

export const MultipleFiles: Story = {
  render: Template,
  args: {
    label: 'Anexos',
    chooseFileText: 'Selecionar arquivos',
    noFileText: 'Nenhum anexo ainda',
    maxFiles: 3,
    accept: '.pdf,image/*',
  },
}
