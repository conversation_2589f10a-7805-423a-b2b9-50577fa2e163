import { Select } from '@thrift/design-system/packages/molecules/Select'
import type { Meta } from '@storybook/react'
import React from 'react'

export default {
  title: 'Molecules/Select',
  component: Select,
} satisfies Meta<typeof Select>

export const Default = () => {
  const [selectedValue, setSelectedValue] = React.useState('')

  return (
    <div className="w-72">
      <Select
        label="Categoria"
        value={selectedValue}
        onChange={(value) => setSelectedValue(value)}
        options={[
          { label: 'Selecione', value: '' },
          { label: 'Financeiro', value: 'finance' },
          { label: 'Estoque', value: 'stock' },
          { label: 'Vendas', value: 'sales' },
        ]}
      />
    </div>
  )
}

export const WithError = () => (
  <div className="w-72">
    <Select
      label="Categoria"
      value=""
      onChange={() => ({})}
      options={[
        { label: 'Selecione', value: '' },
        { label: 'Financeiro', value: 'finance' },
      ]}
      errorMessage="Campo obrigatório"
    />
  </div>
)

export const Disabled = () => (
  <div className="w-72">
    <Select
      label="Categoria"
      value="finance"
      onChange={() => ({})}
      options={[
        { label: 'Financeiro', value: 'finance' },
        { label: 'Estoque', value: 'stock' },
      ]}
      disabled
    />
  </div>
)
