/* eslint-disable complexity */
import { useEffect, useRef, useState } from 'react'
import { ChevronDown } from 'lucide-react'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'
import {
  selectVariants,
  type SelectProps,
} from '@thrift/design-system/packages/molecules/Select'

export const Select: React.FC<SelectProps> = ({
  label,
  value,
  onChange,
  options,
  disabled = false,
  errorMessage,
  placeholder = '',
  emptyMessage = '',
  searchMessage = '',
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [search, setSearch] = useState('')
  const [highlightIndex, setHighlightIndex] = useState(0)

  const listRef = useRef<HTMLUListElement>(null)

  const filtered = options.filter((opt) =>
    opt.label.toLowerCase().includes(search.toLowerCase()),
  )

  const hasError = Boolean(errorMessage)

  const closeDropdown = () => {
    setIsOpen(false)
    setSearch('')
    setHighlightIndex(0)
  }

  useEffect(() => {
    if (listRef.current && isOpen) {
      const item = listRef.current.children[highlightIndex] as HTMLLIElement

      item?.scrollIntoView({ block: 'nearest' })
    }
  }, [highlightIndex, isOpen])

  return (
    <div className="flex flex-col gap-1 relative">
      <Typography variant="label" weight="bold">
        {label}
      </Typography>

      <div
        className="relative w-full"
        tabIndex={0}
        onClick={() => !disabled && setIsOpen((prev) => !prev)}
      >
        <div
          className={
            selectVariants({ error: hasError, disabled }) +
            ' flex items-center justify-between cursor-pointer'
          }
        >
          <Typography className="truncate">
            {options.find((o) => o.value === value)?.label || placeholder}
          </Typography>
          <ChevronDown size="1.6rem" />
        </div>

        {isOpen && !disabled && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded shadow">
            <input
              type="text"
              value={search}
              autoFocus
              onChange={(e) => setSearch(e.target.value)}
              placeholder={searchMessage}
              className="w-full px-2 py-1 h-[2.4rem] text-tiny border-b border-gray-200 outline-none"
              onClick={(e) => e.stopPropagation()}
            />
            <ul ref={listRef} className="max-h-[20rem] overflow-y-auto text-sm">
              {filtered.length > 0 ? (
                filtered.map((opt, index) => {
                  const isSelected = opt.value === value
                  const isHighlighted = index === highlightIndex

                  return (
                    <li
                      key={opt.value}
                      className={`
                        px-2 py-1 cursor-pointer transition-colors
                        ${isHighlighted && !isSelected ? 'bg-gray-100' : ''}
                        hover:bg-gray-200
                      `}
                      onClick={(e) => {
                        e.stopPropagation()
                        onChange(opt.value)
                        closeDropdown()
                      }}
                    >
                      <Typography
                        weight={isSelected ? 'bold' : 'regular'}
                        className={`${isSelected ? 'text-primary' : 'text-black'}`}
                      >
                        {opt.label}
                      </Typography>
                    </li>
                  )
                })
              ) : (
                <li className="px-2 py-1 text-gray-400">{emptyMessage}</li>
              )}
            </ul>
          </div>
        )}
      </div>

      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
