import { tv, type VariantProps } from 'tailwind-variants'

export const selectVariants = tv({
  base: 'w-full h-[4rem] !text-tiny px-[1.6rem] pr-[1.6rem] border !rounded-tiny outline-none transition-colors appearance-none text-black',
  variants: {
    error: {
      true: '!border-red !text-red',
      false: 'border-gray-300',
    },
    disabled: {
      true: 'bg-gray-100 text-gray-400 cursor-not-allowed',
    },
  },
  defaultVariants: {
    error: false,
  },
})

export const iconVariants = tv({
  base: 'pointer-events-none absolute inset-y-0 right-[1.6rem] flex items-center text-black',
  variants: {
    error: {
      true: '!text-red',
    },
    disabled: {
      true: 'text-gray-400',
    },
  },
  defaultVariants: {
    error: false,
  },
})

export type SelectVariantProps = VariantProps<typeof selectVariants>
