export const LogoLeftRightDark: React.FC = () => {
  const cls1 = 'stroke-with-none fill-tertiary'
  const cls2 = 'stroke-with-none fill-gray-200'
  const cls3 = 'stroke-with-none fill-primary'

  return (
    <svg
      id="Layer_1"
      data-name="Layer 1"
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      viewBox="0 0 841.89 595.28"
    >
      <rect
        className={cls3}
        x="199.54"
        y="248.7"
        width="99.07"
        height="99.07"
      />
      <g>
        <g>
          <g>
            <path
              className={cls2}
              d="M364.96,267.7h25.74l-1.04,9.25h-24.7v22.98c0,7.37,4.5,8.76,14.31,8.76,6.7,0,12-.79,12-.79l1.15,9.25s-6.23.99-13.85.99c-12.24,0-25.16-2.88-25.16-18.21v-22.98h-9.12l.92-9.25h8.77l3.81-11.04,7.16-.7v11.74Z"
            />
            <path
              className={cls2}
              d="M402.7,249.3l11.66-.6v22.78c5.54-2.09,14.54-4.58,22.16-4.58,18.47,0,29.67,8.26,29.67,22.88v27.96h-11.66v-27.96c0-8.56-2.77-13.43-19.28-13.43-5.66,0-14.89,1.69-20.89,4.08v37.31h-11.66v-68.45Z"
            />
            <path
              className={cls2}
              d="M524.83,268.1l-2.54,9.35s-4.62-1.1-11.31-1.1c-5.54,0-14.78,1.79-19.97,4.28v37.11h-11.54v-49.35l9.7-.7.81,4.08c5.31-2.09,14.31-4.87,22.39-4.87s12.47,1.19,12.47,1.19Z"
            />
            <path
              className={cls2}
              d="M540.87,260.04c-3.92,0-7.16-2.89-7.16-6.27s3.23-6.27,7.16-6.27,7.27,2.79,7.27,6.27-3.23,6.27-7.27,6.27ZM535.1,268.4l11.66-.6v49.95h-11.66v-49.35Z"
            />
            <path
              className={cls2}
              d="M605.4,249.8l-1.5,9.05c-1.04-.2-6.35-1-12.35-1-11.31,0-13.85,2.39-13.85,8.76v1.1h25.74l-.92,9.25h-24.82v40.79h-11.54v-40.79h-10.74l.93-9.25h9.81v-1.1c0-15.22,13.16-18.11,24.59-18.11,6.23,0,12.47.9,14.66,1.29Z"
            />
            <path
              className={cls2}
              d="M628.25,267.7h25.74l-1.04,9.25h-24.7v22.98c0,7.37,4.5,8.76,14.31,8.76,6.7,0,12.01-.79,12.01-.79l1.15,9.25s-6.23.99-13.85.99c-12.24,0-25.16-2.88-25.16-18.21v-22.98h-9.12l.93-9.25h8.77l3.81-11.04,7.16-.7v11.74Z"
            />
          </g>
          <g>
            <path
              className={cls2}
              d="M538.01,341.79c-.61-.74-.91-1.75-.91-3.03v-6.41h-2.02v-1.13h1.64c.16,0,.27-.05.35-.15.08-.1.12-.28.14-.55l.07-1.93h1.08v2.63h4.41v1.13h-4.41v6.31c0,.67.08,1.24.26,1.69s.45.8.84,1.04c.38.24.87.37,1.47.37.35,0,.69-.02,1.03-.07.34-.05.59-.11.75-.19v1.1c-.21.06-.47.13-.81.19-.34.06-.69.09-1.07.09-1.27,0-2.21-.37-2.82-1.1Z"
            />
            <path
              className={cls2}
              d="M546.93,342.29c-.84-.48-1.5-1.16-1.96-2.04-.46-.88-.69-1.93-.69-3.13s.23-2.31.69-3.23c.46-.91,1.11-1.62,1.95-2.11.84-.5,1.83-.74,2.97-.74,1.06,0,1.98.21,2.76.64.77.42,1.36,1.04,1.77,1.85.41.81.61,1.79.61,2.94v.73h-10.28v-.96h9.08v-.12c0-.84-.16-1.56-.47-2.16s-.76-1.05-1.34-1.35c-.58-.3-1.28-.46-2.11-.46-.89,0-1.66.2-2.32.59-.66.39-1.16.96-1.51,1.7-.35.74-.53,1.64-.53,2.69,0,.97.18,1.82.54,2.55.36.73.87,1.28,1.54,1.66.66.38,1.43.58,2.31.58.74,0,1.39-.12,1.95-.35.56-.24,1.02-.58,1.36-1.02.34-.45.54-.97.59-1.56h1.2c-.03.81-.27,1.53-.71,2.14-.45.61-1.04,1.08-1.8,1.41-.75.33-1.62.49-2.6.49-1.14,0-2.13-.24-2.98-.71Z"
            />
            <path
              className={cls2}
              d="M560.18,342.29c-.84-.48-1.48-1.17-1.94-2.08s-.68-1.97-.68-3.19.23-2.28.69-3.18c.46-.9,1.12-1.59,1.98-2.07.86-.49,1.86-.73,3-.73.96,0,1.81.19,2.56.58s1.34.93,1.76,1.64.63,1.55.63,2.5h-1.17c0-.72-.17-1.35-.5-1.89-.34-.54-.79-.95-1.35-1.23s-1.2-.42-1.93-.42c-.86,0-1.63.2-2.31.59-.68.39-1.2.95-1.56,1.68-.36.73-.54,1.58-.54,2.55s.18,1.84.53,2.57c.35.73.85,1.28,1.5,1.66.65.38,1.4.58,2.24.58.76,0,1.45-.14,2.04-.42.59-.28,1.06-.68,1.41-1.21.35-.52.52-1.15.52-1.87h1.17c0,.94-.22,1.76-.66,2.48-.44.71-1.05,1.26-1.82,1.63s-1.67.56-2.69.56c-1.1,0-2.06-.24-2.9-.71Z"
            />
            <path
              className={cls2}
              d="M571.33,326.56h1.29v5.44c-.01.83-.03,1.65-.05,2.46h.02c.05-.13.09-.26.14-.4.36-.96.97-1.7,1.83-2.23.86-.53,1.92-.8,3.17-.8,1.55,0,2.75.4,3.62,1.21.86.81,1.29,1.94,1.29,3.39v7.18h-1.27v-6.97c0-.76-.15-1.43-.46-1.98-.3-.55-.75-.97-1.34-1.24s-1.32-.41-2.19-.41c-.95,0-1.79.19-2.5.56s-1.27.92-1.66,1.64c-.4.72-.6,1.59-.6,2.6v5.8h-1.29v-16.26Z"
            />
            <path
              className={cls2}
              d="M585.81,331.22h1.27l-.05,3.22h.02c.31-1.06.91-1.9,1.78-2.5.88-.6,1.99-.91,3.35-.91,1.55,0,2.76.4,3.62,1.21.87.81,1.3,1.94,1.3,3.39v7.18h-1.29v-6.97c0-.76-.15-1.43-.45-1.98-.3-.55-.75-.97-1.35-1.24-.6-.27-1.34-.41-2.22-.41-.92,0-1.75.18-2.46.54-.72.36-1.28.89-1.68,1.6-.4.7-.6,1.55-.6,2.53v5.94h-1.27v-11.59Z"
            />
            <path
              className={cls2}
              d="M602.62,342.28c-.86-.49-1.53-1.18-2-2.08-.48-.9-.72-1.95-.72-3.16s.24-2.28.72-3.18c.47-.9,1.15-1.59,2-2.09.86-.5,1.87-.74,3.03-.74s2.13.25,3,.74c.88.49,1.55,1.19,2.03,2.09.48.9.72,1.96.72,3.18s-.24,2.26-.72,3.16c-.47.9-1.15,1.59-2.03,2.08-.88.48-1.88.73-3,.73s-2.17-.24-3.03-.73ZM607.99,341.26c.67-.38,1.2-.93,1.57-1.65.38-.72.56-1.57.56-2.56s-.19-1.85-.56-2.58-.9-1.3-1.57-1.68c-.67-.38-1.45-.57-2.34-.57s-1.68.2-2.35.59c-.67.39-1.2.95-1.57,1.68-.38.73-.56,1.58-.56,2.57s.19,1.84.56,2.56c.38.72.9,1.27,1.57,1.65.67.38,1.45.58,2.35.58s1.67-.19,2.34-.58Z"
            />
            <path
              className={cls2}
              d="M614.39,326.56h1.29v16.26h-1.29v-16.26Z"
            />
            <path
              className={cls2}
              d="M621.44,342.28c-.86-.49-1.53-1.18-2-2.08-.48-.9-.72-1.95-.72-3.16s.24-2.28.72-3.18c.47-.9,1.15-1.59,2-2.09.86-.5,1.87-.74,3.03-.74s2.13.25,3,.74c.88.49,1.55,1.19,2.03,2.09.48.9.72,1.96.72,3.18s-.24,2.26-.72,3.16c-.47.9-1.15,1.59-2.03,2.08-.88.48-1.88.73-3,.73s-2.17-.24-3.03-.73ZM626.81,341.26c.67-.38,1.2-.93,1.57-1.65.38-.72.56-1.57.56-2.56s-.19-1.85-.56-2.58-.9-1.3-1.57-1.68c-.67-.38-1.45-.57-2.34-.57s-1.68.2-2.35.59c-.67.39-1.2.95-1.57,1.68-.38.73-.56,1.58-.56,2.57s.19,1.84.56,2.56c.38.72.9,1.27,1.57,1.65.67.38,1.45.58,2.35.58s1.67-.19,2.34-.58Z"
            />
            <path
              className={cls2}
              d="M635.13,347.37c-.88-.27-1.56-.65-2.03-1.15s-.7-1.11-.7-1.83c0-.66.24-1.21.71-1.66s1.08-.67,1.82-.67v-.19l1.71.63c-.95,0-1.69.15-2.2.46-.52.3-.78.78-.78,1.42,0,.72.41,1.28,1.22,1.69.81.41,1.95.61,3.4.61s2.52-.22,3.24-.66c.72-.44,1.08-1.04,1.08-1.8,0-.55-.21-.97-.62-1.27-.41-.3-1.01-.45-1.77-.45h-3.57c-.52,0-.98-.1-1.39-.29-.42-.2-.73-.47-.94-.81-.21-.34-.32-.74-.32-1.2,0-.39.06-.73.19-1.01.13-.28.29-.5.5-.64v-.14l.87.45s-.06.04-.09.06-.06.05-.07.08c-.08.11-.15.25-.2.43-.06.18-.08.37-.08.57,0,.28.06.52.19.73.13.21.3.36.53.47.23.11.51.16.86.16h3.71c.75,0,1.38.11,1.9.32.52.21.91.52,1.2.94s.42.91.42,1.49c0,1.13-.5,2.01-1.49,2.66-.99.65-2.37.97-4.12.97-1.22,0-2.27-.13-3.16-.4ZM635.17,338.79c-.7-.33-1.25-.82-1.63-1.44-.38-.63-.57-1.35-.57-2.18s.19-1.56.57-2.18c.38-.62.93-1.11,1.64-1.44.71-.34,1.54-.51,2.5-.51s1.79.17,2.5.51c.71.33,1.26.82,1.65,1.44.39.63.59,1.35.59,2.18s-.2,1.56-.59,2.18c-.39.62-.94,1.11-1.65,1.44-.71.34-1.55.51-2.5.51s-1.81-.17-2.51-.51ZM639.52,337.83c.52-.24.93-.59,1.21-1.05.28-.46.42-1,.42-1.61s-.14-1.14-.42-1.6c-.28-.45-.69-.81-1.21-1.05-.52-.25-1.14-.38-1.84-.38s-1.32.13-1.84.38-.93.6-1.21,1.05c-.28.45-.42.99-.42,1.6s.14,1.15.42,1.61c.28.46.68.81,1.21,1.05s1.14.36,1.84.36,1.32-.12,1.84-.36ZM640.7,331.98l.07-.12c-.13-.16-.23-.35-.3-.58-.08-.23-.12-.46-.12-.69,0-.5.17-.89.52-1.16.34-.28.82-.41,1.43-.41.42,0,.76.05,1.03.14v1.08c-.09-.06-.22-.11-.38-.13-.16-.02-.33-.04-.52-.04-.39,0-.7.09-.92.27-.22.18-.33.44-.33.79,0,.24.04.5.13.78.09.28.2.57.34.87.02.08.03.13.05.16.02.03.04.08.07.14l-1.08-1.1Z"
            />
            <path
              className={cls2}
              d="M647,347.58c-.27-.06-.49-.13-.64-.19v-1.08c.16.06.35.12.6.16s.48.07.72.07c.44,0,.81-.1,1.11-.29.3-.19.59-.52.87-.98.28-.46.61-1.15,1-2.05.03-.06.06-.13.08-.19s.05-.13.08-.21h-.4l-5.28-11.59h1.5l4.65,10.35h.05l4.11-10.35h1.31l-4.69,11.76c-.5,1.27-.94,2.23-1.31,2.89s-.79,1.12-1.23,1.4c-.45.27-1.02.41-1.72.41-.25,0-.51-.03-.79-.09Z"
            />
          </g>
        </g>
        <path
          className={cls1}
          d="M283.76,235.52h-73.02c-14.14,0-25.61,11.46-25.61,25.61v73.02c0,14.14,11.46,25.61,25.61,25.61h73.02c14.14,0,25.61-11.46,25.61-25.61v-73.02c0-14.14-11.46-25.61-25.61-25.61ZM229.18,270.75c2.24-7.86,9.48-13.63,18.05-13.63,5.94,0,11.21,2.81,14.65,7.13l2.79-2.79c1.02-1.02,2.77-.3,2.77,1.15v12.21c0,.57-.46,1.03-1.03,1.03h-12.21c-1.44,0-2.17-1.74-1.15-2.77l2.69-2.69c.36-.36.42-.95.11-1.36-2.02-2.56-5.11-4.22-8.62-4.22-4.52,0-8.38,2.73-10.1,6.61-.16.37-.52.62-.92.62h-6.02c-.67,0-1.2-.65-1.01-1.29ZM230.19,279.73h6.02c.41,0,.76.25.92.62,1.72,3.88,5.58,6.61,10.1,6.61s8.38-2.73,10.1-6.61c.16-.37.52-.62.92-.62h6.02c.67,0,1.2.65,1.01,1.29-2.24,7.86-9.48,13.63-18.05,13.63s-15.81-5.77-18.05-13.63c-.18-.65.34-1.29,1.01-1.29ZM286,329.62c-2.1,5.31-7.1,8.54-12.47,8.54h-52.55c-5.37,0-10.37-3.23-12.47-8.53-.91-2.29-1.01-4.82-.68-7.27l5.26-38.76c.73-5.41,4.7-9.71,9.77-11.08.64-.17,1.27.36,1.27,1.02v6.04c0,.38-.22.71-.56.89-1.51.85-2.62,2.36-2.86,4.17l-5.35,39.4c-.22,1.64.26,3.24,1.35,4.49,1.09,1.25,2.61,1.94,4.27,1.94h52.55c1.66,0,3.18-.69,4.27-1.94,1.09-1.25,1.57-2.85,1.34-4.49l-5.39-39.4c-.24-1.8-1.34-3.3-2.83-4.14-.33-.19-.55-.51-.55-.89v-6.05c0-.66.63-1.19,1.27-1.02,5.05,1.39,9,5.67,9.73,11.06l5.3,38.75c.33,2.45.23,4.99-.68,7.28Z"
        />
      </g>
    </svg>
  )
}
