export const LogoTopDownBlack: React.FC = () => {
  const cls1 = 'stroke-with-none fill-black'

  return (
    <svg
      id="Layer_1"
      data-name="Layer 1"
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      viewBox="0 0 841.89 595.28"
    >
      <g>
        <path
          className={cls1}
          d="M245.24,340.94h33.73l-1.36,12.12h-32.37v30.12c0,9.65,5.9,11.48,18.76,11.48,8.77,0,15.73-1.04,15.73-1.04l1.51,12.12s-8.17,1.3-18.15,1.3c-16.03,0-32.98-3.78-32.98-23.86v-30.12h-11.95l1.21-12.12h11.5l4.99-14.47,9.38-.92v15.39Z"
        />
        <path
          className={cls1}
          d="M294.7,316.82l15.28-.78v29.85c7.26-2.73,19.06-6,29.04-6,24.2,0,38.88,10.82,38.88,29.99v36.64h-15.28v-36.64c0-11.21-3.63-17.6-25.26-17.6-7.41,0-19.51,2.22-27.38,5.35v48.89h-15.28v-89.7Z"
        />
        <path
          className={cls1}
          d="M454.74,341.46l-3.33,12.26s-6.05-1.44-14.82-1.44c-7.26,0-19.36,2.35-26.17,5.61v48.63h-15.13v-64.67l12.71-.91,1.06,5.34c6.96-2.73,18.76-6.39,29.34-6.39s16.34,1.56,16.34,1.56Z"
        />
        <path
          className={cls1}
          d="M475.76,330.9c-5.14,0-9.38-3.78-9.38-8.21s4.24-8.21,9.38-8.21,9.53,3.65,9.53,8.21-4.23,8.21-9.53,8.21ZM468.2,341.85l15.28-.78v65.45h-15.28v-64.67Z"
        />
        <path
          className={cls1}
          d="M560.32,317.47l-1.97,11.86c-1.36-.26-8.32-1.31-16.18-1.31-14.82,0-18.15,3.13-18.15,11.47v1.44h33.73l-1.21,12.12h-32.52v53.46h-15.13v-53.46h-14.07l1.21-12.12h12.86v-1.44c0-19.95,17.24-23.72,32.22-23.72,8.17,0,16.34,1.17,19.21,1.69Z"
        />
        <path
          className={cls1}
          d="M590.26,340.94h33.73l-1.36,12.12h-32.37v30.12c0,9.65,5.9,11.48,18.76,11.48,8.77,0,15.73-1.04,15.73-1.04l1.51,12.12s-8.17,1.3-18.15,1.3c-16.03,0-32.98-3.78-32.98-23.86v-30.12h-11.95l1.21-12.12h11.49l4.99-14.47,9.38-.92v15.39Z"
        />
      </g>
      <g>
        <path
          className={cls1}
          d="M346.33,438.02c-.8-.96-1.2-2.29-1.2-3.97v-8.39h-2.65v-1.48h2.15c.2,0,.36-.07.46-.2.1-.13.16-.37.18-.72l.09-2.52h1.41v3.44h5.78v1.48h-5.78v8.27c0,.88.11,1.62.34,2.22s.59,1.05,1.09,1.37c.5.32,1.14.48,1.92.48.45,0,.9-.03,1.36-.09.45-.06.78-.15.98-.25v1.45c-.27.08-.62.17-1.06.25-.44.08-.91.12-1.4.12-1.66,0-2.89-.48-3.69-1.44Z"
        />
        <path
          className={cls1}
          d="M358.02,438.68c-1.11-.63-1.96-1.52-2.56-2.68-.61-1.16-.91-2.53-.91-4.1s.3-3.03.91-4.23c.6-1.2,1.46-2.12,2.55-2.76,1.1-.65,2.4-.97,3.89-.97,1.39,0,2.6.28,3.62.83,1.01.55,1.79,1.36,2.32,2.43.53,1.07.8,2.35.8,3.85v.95h-13.47v-1.26h11.9v-.15c0-1.11-.2-2.05-.61-2.83-.41-.78-.99-1.37-1.75-1.77-.76-.4-1.68-.6-2.77-.6-1.17,0-2.18.26-3.04.77-.86.51-1.52,1.26-1.99,2.23-.46.97-.69,2.15-.69,3.52,0,1.27.23,2.38.71,3.34.47.95,1.14,1.68,2.02,2.18.87.5,1.88.76,3.03.76.96,0,1.82-.15,2.55-.46.74-.31,1.33-.76,1.79-1.34.45-.58.71-1.27.77-2.05h1.57c-.04,1.07-.35,2-.94,2.8-.58.8-1.37,1.42-2.35,1.85-.98.43-2.12.65-3.41.65-1.49,0-2.8-.31-3.9-.94Z"
        />
        <path
          className={cls1}
          d="M375.38,438.68c-1.1-.63-1.94-1.53-2.54-2.72-.6-1.19-.89-2.58-.89-4.18s.3-2.99.91-4.17c.6-1.18,1.47-2.08,2.6-2.72,1.13-.64,2.44-.96,3.94-.96,1.25,0,2.37.25,3.35.76.98.5,1.75,1.22,2.31,2.15s.83,2.03.83,3.28h-1.54c0-.94-.22-1.77-.66-2.48-.44-.71-1.03-1.24-1.77-1.61s-1.58-.55-2.52-.55c-1.13,0-2.14.26-3.03.77-.89.51-1.57,1.24-2.05,2.2-.47.95-.71,2.07-.71,3.34s.23,2.41.69,3.37c.46.95,1.12,1.68,1.97,2.18.85.5,1.83.76,2.94.76,1,0,1.9-.18,2.68-.56.78-.37,1.39-.89,1.84-1.58.45-.69.68-1.5.68-2.45h1.54c0,1.23-.29,2.31-.86,3.24-.58.93-1.37,1.65-2.38,2.14-1.01.49-2.19.74-3.52.74-1.44,0-2.7-.31-3.8-.94Z"
        />
        <path
          className={cls1}
          d="M390,418.06h1.69v7.13c-.02,1.09-.04,2.16-.06,3.23h.03c.06-.17.12-.34.18-.52.47-1.25,1.27-2.23,2.4-2.92,1.13-.7,2.51-1.05,4.15-1.05,2.03,0,3.61.53,4.74,1.59,1.13,1.06,1.69,2.54,1.69,4.44v9.41h-1.66v-9.13c0-1-.2-1.87-.6-2.6-.4-.73-.99-1.27-1.75-1.63s-1.73-.54-2.88-.54c-1.25,0-2.34.25-3.28.74-.93.49-1.66,1.21-2.18,2.15-.52.94-.79,2.08-.79,3.41v7.59h-1.69v-21.31Z"
        />
        <path
          className={cls1}
          d="M408.97,424.17h1.66l-.06,4.22h.03c.41-1.39,1.19-2.49,2.34-3.28,1.15-.79,2.61-1.19,4.4-1.19,2.03,0,3.61.53,4.75,1.59,1.14,1.06,1.71,2.54,1.71,4.44v9.41h-1.69v-9.13c0-1-.19-1.87-.58-2.6-.39-.73-.98-1.27-1.77-1.63-.79-.36-1.76-.54-2.91-.54-1.21,0-2.29.23-3.23.71-.94.47-1.68,1.17-2.2,2.09-.52.92-.78,2.03-.78,3.32v7.78h-1.66v-15.19Z"
        />
        <path
          className={cls1}
          d="M430.99,438.66c-1.13-.64-2-1.54-2.63-2.72-.63-1.18-.94-2.56-.94-4.14s.31-2.99.94-4.17c.62-1.17,1.5-2.09,2.63-2.73,1.13-.65,2.45-.97,3.97-.97s2.79.32,3.94.97c1.15.65,2.03,1.56,2.66,2.73.63,1.18.94,2.57.94,4.17s-.31,2.96-.94,4.14c-.62,1.18-1.51,2.08-2.66,2.72-1.15.63-2.46.95-3.94.95s-2.84-.32-3.97-.95ZM438.03,437.32c.88-.5,1.57-1.22,2.06-2.17.49-.94.74-2.06.74-3.35s-.25-2.42-.74-3.38-1.18-1.7-2.06-2.2c-.88-.5-1.9-.75-3.07-.75s-2.2.26-3.08.77c-.88.51-1.57,1.24-2.06,2.2-.49.95-.74,2.07-.74,3.37s.25,2.41.74,3.35c.49.94,1.18,1.67,2.06,2.17.88.5,1.9.76,3.08.76s2.19-.25,3.07-.76Z"
        />
        <path className={cls1} d="M446.43,418.06h1.69v21.31h-1.69v-21.31Z" />
        <path
          className={cls1}
          d="M455.65,438.66c-1.13-.64-2-1.54-2.63-2.72-.63-1.18-.94-2.56-.94-4.14s.31-2.99.94-4.17c.62-1.17,1.5-2.09,2.63-2.73,1.13-.65,2.45-.97,3.97-.97s2.79.32,3.94.97c1.15.65,2.03,1.56,2.66,2.73.63,1.18.94,2.57.94,4.17s-.31,2.96-.94,4.14c-.62,1.18-1.51,2.08-2.66,2.72-1.15.63-2.46.95-3.94.95s-2.84-.32-3.97-.95ZM462.7,437.32c.88-.5,1.57-1.22,2.06-2.17.49-.94.74-2.06.74-3.35s-.25-2.42-.74-3.38c-.49-.96-1.18-1.7-2.06-2.2-.88-.5-1.9-.75-3.07-.75s-2.2.26-3.08.77c-.88.51-1.57,1.24-2.06,2.2-.49.95-.74,2.07-.74,3.37s.25,2.41.74,3.35c.49.94,1.18,1.67,2.06,2.17.88.5,1.9.76,3.08.76s2.19-.25,3.07-.76Z"
        />
        <path
          className={cls1}
          d="M473.6,445.33c-1.16-.35-2.05-.85-2.66-1.51-.61-.66-.92-1.46-.92-2.4,0-.86.31-1.59.94-2.17s1.42-.88,2.38-.88v-.25l2.24.83c-1.25,0-2.21.2-2.89.6-.68.4-1.02,1.02-1.02,1.86,0,.94.53,1.68,1.6,2.21,1.06.53,2.55.8,4.46.8s3.3-.29,4.24-.86c.94-.57,1.42-1.36,1.42-2.37,0-.72-.27-1.27-.82-1.66-.54-.39-1.32-.58-2.32-.58h-4.68c-.68,0-1.29-.13-1.83-.38-.55-.26-.95-.61-1.23-1.06-.28-.45-.41-.97-.41-1.57,0-.51.08-.95.25-1.32.17-.37.38-.65.65-.83v-.18l1.14.58s-.08.05-.12.08-.07.07-.09.11c-.1.14-.19.33-.26.57-.07.23-.11.49-.11.75,0,.37.08.69.25.95.17.27.4.47.69.61.3.15.67.22,1.12.22h4.86c.98,0,1.81.14,2.49.42.68.28,1.2.69,1.57,1.23s.55,1.19.55,1.95c0,1.47-.65,2.64-1.95,3.49-1.3.85-3.1,1.27-5.4,1.27-1.6,0-2.98-.17-4.14-.52ZM473.65,434.09c-.92-.44-1.64-1.07-2.14-1.89-.5-.82-.75-1.77-.75-2.86s.25-2.04.75-2.86c.5-.82,1.22-1.45,2.15-1.89.93-.44,2.02-.66,3.28-.66s2.34.22,3.28.66c.93.44,1.65,1.07,2.17,1.89.51.82.77,1.77.77,2.86s-.26,2.04-.77,2.86c-.51.82-1.23,1.45-2.17,1.89-.93.44-2.03.66-3.28.66s-2.37-.22-3.29-.66ZM479.35,432.83c.68-.32,1.21-.78,1.58-1.38.37-.61.55-1.31.55-2.11s-.18-1.5-.55-2.09c-.37-.59-.9-1.06-1.58-1.38-.69-.33-1.49-.49-2.42-.49s-1.73.17-2.42.49-1.21.79-1.58,1.38c-.37.6-.56,1.29-.56,2.09s.18,1.5.56,2.11c.37.6.89,1.06,1.58,1.38s1.49.48,2.42.48,1.73-.16,2.42-.48ZM480.9,425.16l.09-.15c-.17-.21-.3-.46-.4-.76-.1-.3-.15-.6-.15-.91,0-.66.23-1.16.68-1.52.45-.36,1.08-.54,1.88-.54.55,0,1,.06,1.35.18v1.41c-.12-.08-.29-.14-.49-.17-.21-.03-.43-.05-.68-.05-.51,0-.91.12-1.2.35-.29.24-.43.58-.43,1.03,0,.31.06.65.17,1.02.12.37.26.75.45,1.14.02.1.04.17.06.22.02.04.05.1.09.18l-1.41-1.44Z"
        />
        <path
          className={cls1}
          d="M489.16,445.61c-.36-.08-.64-.17-.84-.25v-1.42c.2.08.46.15.78.22s.63.09.94.09c.57,0,1.06-.13,1.46-.38.4-.25.78-.69,1.14-1.29.36-.61.79-1.5,1.31-2.69.04-.08.08-.17.11-.25s.07-.17.11-.28h-.52l-6.92-15.19h1.97l6.09,13.57h.06l5.38-13.57h1.72l-6.15,15.41c-.66,1.66-1.23,2.92-1.72,3.78s-1.03,1.47-1.62,1.83c-.58.36-1.34.54-2.26.54-.33,0-.67-.04-1.03-.12Z"
        />
      </g>
      <path
        className={cls1}
        d="M460.62,149.42h-76.81c-14.88,0-26.94,12.06-26.94,26.94v76.81c0,14.88,12.06,26.94,26.94,26.94h76.81c14.88,0,26.94-12.06,26.94-26.94v-76.81c0-14.88-12.06-26.94-26.94-26.94ZM403.2,186.47c2.36-8.27,9.98-14.34,18.99-14.34,6.25,0,11.79,2.96,15.41,7.5l2.94-2.94c1.07-1.07,2.91-.31,2.91,1.21v12.85c0,.6-.48,1.08-1.08,1.08h-12.85c-1.52,0-2.28-1.84-1.21-2.91l2.83-2.83c.38-.38.45-1,.11-1.43-2.12-2.69-5.38-4.44-9.07-4.44-4.75,0-8.81,2.87-10.62,6.96-.17.39-.54.65-.97.65h-6.33c-.71,0-1.26-.68-1.06-1.36ZM404.27,195.93h6.33c.43,0,.8.26.97.65,1.81,4.09,5.87,6.96,10.62,6.96s8.81-2.87,10.62-6.96c.17-.39.54-.65.97-.65h6.33c.71,0,1.26.68,1.06,1.36-2.36,8.27-9.98,14.34-18.99,14.34s-16.63-6.07-18.99-14.34c-.19-.68.36-1.36,1.06-1.36ZM462.98,248.41c-2.21,5.59-7.47,8.98-13.12,8.98h-55.28c-5.65,0-10.91-3.4-13.12-8.97-.96-2.41-1.07-5.07-.72-7.64l5.54-40.77c.77-5.69,4.94-10.21,10.28-11.66.67-.18,1.34.37,1.34,1.07v6.35c0,.4-.24.74-.58.94-1.59.89-2.75,2.48-3.01,4.39l-5.63,41.44c-.23,1.73.27,3.41,1.42,4.73,1.15,1.32,2.74,2.04,4.49,2.04h55.28c1.75,0,3.34-.73,4.5-2.04,1.15-1.32,1.65-2.99,1.41-4.73l-5.67-41.44c-.26-1.89-1.41-3.47-2.98-4.36-.35-.2-.58-.54-.58-.94v-6.36c0-.7.67-1.26,1.34-1.07,5.31,1.46,9.46,5.97,10.24,11.63l5.57,40.76c.35,2.57.24,5.25-.71,7.66Z"
      />
    </svg>
  )
}
