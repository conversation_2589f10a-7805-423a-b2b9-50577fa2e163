import type { Meta, StoryObj } from '@storybook/react-vite'
import {
  type LogoProps,
  Logo,
  LogoVariantOptions,
  LogoModeOptions,
} from '@thrift/design-system/packages/molecules/Logo'

const meta = {
  title: 'Molecules/Logo',
  component: Logo,
  argTypes: {
    variant: {
      options: Object.values(LogoVariantOptions),
      control: { type: 'select' },
    },
    mode: {
      options: Object.values(LogoModeOptions),
      control: { type: 'select' },
    },
  },
} satisfies Meta<LogoProps>

export default meta

type Story = StoryObj<LogoProps>

export const LogoDefault: Story = {
  name: 'Logo',
  args: {
    variant: LogoVariantOptions.TOP_DOWN,
    mode: LogoModeOptions.LIGHT,
  },
}
