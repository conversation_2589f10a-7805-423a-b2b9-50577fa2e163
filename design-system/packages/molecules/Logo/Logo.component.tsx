import { useMemo } from 'react'
import {
  type LogoProps,
  LogoVariantOptions,
  LogoModeOptions,
  LogoTopDownLight,
  LogoTopDownDark,
  LogoTopDownBlack,
  LogoTopDownWhite,
  LogoLeftRightLight,
  LogoLeftRightDark,
  LogoLeftRightBlack,
  LogoLeftRightWhite,
  LogoIconFillLight,
  LogoIconFillDark,
  LogoIconFillBlack,
  LogoIconFillWhite,
  LogoIconLight,
  LogoIconDark,
  LogoIconBlack,
  LogoIconWhite,
} from '@thrift/design-system/packages/molecules/Logo'

export const Logo: React.FC<LogoProps> = ({
  variant,
  mode,
  size = 'size-1/3',
}) => {
  const LogoOptions = useMemo(
    () => ({
      [LogoVariantOptions.TOP_DOWN]: {
        [LogoModeOptions.LIGHT]: LogoTopDownLight,
        [LogoModeOptions.DARK]: LogoTopDownDark,
        [LogoModeOptions.BLACK]: LogoTopDownBlack,
        [LogoModeOptions.WHITE]: LogoTopDownWhite,
      },
      [LogoVariantOptions.LEFT_RIGHT]: {
        [LogoModeOptions.LIGHT]: LogoLeftRightLight,
        [LogoModeOptions.DARK]: LogoLeftRightDark,
        [LogoModeOptions.BLACK]: LogoLeftRightBlack,
        [LogoModeOptions.WHITE]: LogoLeftRightWhite,
      },
      [LogoVariantOptions.ICON_FILL]: {
        [LogoModeOptions.LIGHT]: LogoIconFillLight,
        [LogoModeOptions.DARK]: LogoIconFillDark,
        [LogoModeOptions.BLACK]: LogoIconFillBlack,
        [LogoModeOptions.WHITE]: LogoIconFillWhite,
      },
      [LogoVariantOptions.ICON]: {
        [LogoModeOptions.LIGHT]: LogoIconLight,
        [LogoModeOptions.DARK]: LogoIconDark,
        [LogoModeOptions.BLACK]: LogoIconBlack,
        [LogoModeOptions.WHITE]: LogoIconWhite,
      },
    }),
    [],
  )

  const LogoComponent = LogoOptions[variant][mode] || LogoTopDownLight

  return (
    <div className={`${size}`}>
      <LogoComponent />
    </div>
  )
}
