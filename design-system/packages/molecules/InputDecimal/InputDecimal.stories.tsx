import type { Meta } from '@storybook/react-vite'
import { InputDecimal } from '@thrift/design-system/packages/molecules/InputDecimal'
import React from 'react'

export default {
  title: 'Molecules/InputDecimal',
  component: InputDecimal,
} satisfies Meta<typeof InputDecimal>

export const Default = () => {
  const [text, setText] = React.useState('')

  return (
    <div className="w-72">
      <InputDecimal
        label="Decimal"
        value={text}
        onChange={(t) => setText(t as string)}
        name="decimal-input"
      />
    </div>
  )
}

export const WithError = () => (
  <div className="w-72">
    <InputDecimal
      label="Decimal"
      value=""
      onChange={() => ({})}
      errorMessage="Campo obrigatório"
    />
  </div>
)

export const Disabled = () => (
  <div className="w-72">
    <InputDecimal
      label="Decimal"
      value="50,50"
      onChange={() => ({})}
      disabled
    />
  </div>
)
