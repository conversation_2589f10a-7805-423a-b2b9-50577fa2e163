import { z } from 'zod'
import type { InputDecimalSchemaProps } from '@thrift/design-system/packages/molecules/InputDecimal'

export const InputDecimalSchema = ({
  required,
  onlyNumbersErrorMessage,
}: InputDecimalSchemaProps) => {
  const schema = z.string()

  if (required?.value) {
    schema.nonempty(required.message)
  }

  schema
    .refine((value) => value !== 'nill', {
      message: required.message,
    })
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return !!Number(replacedValue)
    }, onlyNumbersErrorMessage)

  return schema
}
