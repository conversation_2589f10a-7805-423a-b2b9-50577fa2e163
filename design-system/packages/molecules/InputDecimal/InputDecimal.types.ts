import type { InputHTMLAttributes } from 'react'

export type InputDecimalProps = {
  label?: string
  name?: string
  errorMessage?: string
  placeholder?: string
  disabled?: boolean
  onChange?: (text: string) => void
  decimalSeparator?: string
  thousandSeparator?: string
} & InputHTMLAttributes<HTMLInputElement>

type ValueType<T> = {
  value: T
  message: string
}

export type InputDecimalSchemaProps = {
  required: ValueType<boolean>
  onlyNumbersErrorMessage?: string
}
