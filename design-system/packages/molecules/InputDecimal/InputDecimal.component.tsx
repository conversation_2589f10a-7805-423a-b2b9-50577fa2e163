import {
  inputDecimalVariants,
  type InputDecimalProps,
} from '@thrift/design-system/packages/molecules/InputDecimal'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const InputDecimal = ({
  label,
  name,
  errorMessage,
  placeholder = '',
  disabled = false,
  onChange,
  decimalSeparator = ',',
  thousandSeparator = '.',
  ...rest
}: InputDecimalProps) => {
  const hasError = Boolean(errorMessage)

  const formatDecimal = (value: string) => {
    const cleaned = value.replace(/\D/g, '')

    const numeric = (Number(cleaned) / 100).toFixed(2)

    const [int, dec] = numeric.split('.')

    const formattedInt = int.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator)

    return `${formattedInt}${decimalSeparator}${dec}`
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value

    const masked = formatDecimal(raw)

    onChange?.(masked)
  }

  return (
    <div className="flex flex-col gap-1">
      <Typography weight="bold" htmlFor={name}>
        {label}
      </Typography>
      <input
        id={name}
        name={name}
        placeholder={placeholder}
        className={inputDecimalVariants({
          error: hasError,
          disabled,
        })}
        onChange={handleChange}
        {...rest}
      />
      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
