import type { Meta } from '@storybook/react-vite'
import { InputSearch } from '@thrift/design-system/packages/molecules/InputSearch'
import React from 'react'

export default {
  title: 'Molecules/InputSearch',
  component: InputSearch,
} satisfies Meta<typeof InputSearch>

export const Default = () => {
  const [text, setText] = React.useState('')

  return (
    <div className="w-72">
      <InputSearch
        label="Procurar"
        value={text}
        onChange={(e) => setText(e.target.value)}
        name="search-input"
      />
    </div>
  )
}

export const Disabled = () => (
  <div className="w-72">
    <InputSearch
      label="Procurar"
      value="Texto de pesquisa"
      onChange={() => ({})}
      disabled
    />
  </div>
)
