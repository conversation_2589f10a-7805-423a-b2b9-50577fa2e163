import {
  inputSearchVariants,
  type InputSearchProps,
} from '@thrift/design-system/packages/molecules/InputSearch'
import { Search } from 'lucide-react'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const InputSearch = ({
  label,
  name,
  placeholder = '',
  disabled = false,
  ...rest
}: InputSearchProps) => (
  <div className="flex flex-col gap-1">
    <Typography variant="label" weight="bold" htmlFor={name}>
      {label}
    </Typography>
    <div className="relative w-full z-0">
      <input
        id={name}
        name={name}
        placeholder={placeholder}
        className={inputSearchVariants({
          disabled,
        })}
        {...rest}
      />
      <label htmlFor={name}>
        <div className="absolute right-[1.6rem] top-1/2 -translate-y-1/2">
          {<Search className="w-6 h-6" />}
        </div>
      </label>
    </div>
  </div>
)
