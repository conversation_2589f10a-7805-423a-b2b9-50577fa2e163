import type { Meta } from '@storybook/react-vite'
import { InputPhone } from '@thrift/design-system/packages/molecules/InputPhone'
import React from 'react'

export default {
  title: 'Molecules/InputPhone',
  component: InputPhone,
} satisfies Meta<typeof InputPhone>

export const Default = () => {
  const [text, setText] = React.useState('')

  return (
    <div className="w-72">
      <InputPhone
        label="Telefone"
        value={text}
        onChange={(t) => setText(t as string)}
        name="telefone"
      />
    </div>
  )
}

export const WithError = () => (
  <div className="w-72">
    <InputPhone
      label="Telefone"
      value=""
      onChange={() => ({})}
      errorMessage="Campo obrigatório"
    />
  </div>
)

export const Disabled = () => (
  <div className="w-72">
    <InputPhone
      label="Telefone"
      value="(11) 99999-9999"
      onChange={() => ({})}
      disabled
    />
  </div>
)
