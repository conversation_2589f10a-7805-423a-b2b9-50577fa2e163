import { z } from 'zod'
import type { InputPhoneSchemaProps } from '@thrift/design-system/packages/molecules/InputPhone'

export const InputPhoneSchema = ({
  required,
  invalidLengthErrorMessage,
  onlyNumbersErrorMessage,
}: InputPhoneSchemaProps) => {
  const schema = z.string()

  if (required?.value) {
    schema.nonempty(required.message)
  }

  schema
    .refine((value) => value !== 'nill', {
      message: required.message,
    })
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return replacedValue.length < 10 || replacedValue.length > 11
    }, invalidLengthErrorMessage)
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return !!Number(replacedValue)
    }, onlyNumbersErrorMessage)

  return schema
}
