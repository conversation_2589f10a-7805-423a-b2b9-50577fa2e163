import type { InputHTMLAttributes } from 'react'

export type InputPhoneProps = {
  label?: string
  name?: string
  errorMessage?: string
  placeholder?: string
  disabled?: boolean
  onChange?: (text: string) => void
} & InputHTMLAttributes<HTMLInputElement>

type ValueType<T> = {
  value: T
  message: string
}

export type InputPhoneSchemaProps = {
  required: ValueType<boolean>
  onlyNumbersErrorMessage?: string
  invalidLengthErrorMessage?: string
}
