import {
  inputPhoneVariants,
  type InputPhoneProps,
} from '@thrift/design-system/packages/molecules/InputPhone'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const InputPhone = ({
  label,
  name,
  errorMessage,
  placeholder = '',
  disabled = false,
  onChange,
  ...rest
}: InputPhoneProps) => {
  const hasError = Boolean(errorMessage)

  const formatPhone = (phone: string) => {
    const onlyDigits = phone.replace(/\D/g, '')

    if (onlyDigits.length <= 10) {
      return onlyDigits
        .replace(/^(\d{2})(\d)/, '($1) $2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .slice(0, 14)
    }

    return onlyDigits
      .replace(/^(\d{2})(\d)/, '($1) $2')
      .replace(/(\d{1}) (\d{4})(\d)/, '($1) $2-$3')
      .replace(/(\d{5})(\d)/, '$1-$2')
      .slice(0, 15)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value

    const masked = formatPhone(raw)

    onChange?.(masked)
  }

  return (
    <div className="flex flex-col gap-1">
      <Typography variant="label" weight="bold" htmlFor={name}>
        {label}
      </Typography>
      <input
        id={name}
        name={name}
        placeholder={placeholder}
        className={inputPhoneVariants({
          error: hasError,
          disabled,
        })}
        onChange={handleChange}
        {...rest}
      />
      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
