import {
  checkboxGroupVariants,
  type CheckboxGroupProps,
} from '@thrift/design-system/packages/molecules/CheckboxGroup'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  name,
  options,
  values = [],
  onChange,
  errorMessage,
  disabled = false,
}) => {
  const hasError = <PERSON><PERSON><PERSON>(errorMessage)

  const handleToggle = (value: string) => {
    const newValues = values.includes(value)
      ? values.filter((v) => v !== value)
      : [...values, value]

    onChange?.(newValues)
  }

  return (
    <div className="flex flex-col gap-2">
      {options.map((option) => {
        const checked = values.includes(option.value)
        const isDisabled = disabled || option.disabled

        return (
          <label
            key={option.value}
            className={checkboxGroupVariants({
              disabled: isDisabled,
            })}
          >
            <input
              type="checkbox"
              name={name}
              value={option.value}
              checked={checked}
              onChange={() => handleToggle(option.value)}
              disabled={isDisabled}
              className="mt-1.5 h-[1.6rem] w-[1.6rem] accent-secondary cursor-pointer"
            />

            <div>
              {option.label && (
                <Typography weight="bold">{option.label}</Typography>
              )}
              {option.description && (
                <Typography className="text-gray-900">
                  {option.description}
                </Typography>
              )}
            </div>
          </label>
        )
      })}

      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
