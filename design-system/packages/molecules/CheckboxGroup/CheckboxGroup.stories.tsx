import type { Meta, StoryObj } from '@storybook/react-vite'
import { CheckboxGroup } from '@thrift/design-system/packages/molecules/CheckboxGroup'
import { useState } from 'react'

export default {
  title: 'Molecules/CheckboxGroup',
  component: CheckboxGroup,
} satisfies Meta<typeof CheckboxGroup>

type Story = StoryObj<typeof CheckboxGroup>

const options = [
  { value: 'pix', label: 'Pix', description: 'Receba instantaneamente' },
  { value: 'boleto', label: 'Boleto', description: 'Até 3 dias úteis' },
  {
    value: 'cartao',
    label: 'Cartão',
    description: 'Use seu cartão de crédito',
  },
]

const Template = (args: React.ComponentProps<typeof CheckboxGroup>) => {
  const [selected, setSelected] = useState<string[]>(args.values || [])

  return <CheckboxGroup {...args} values={selected} onChange={setSelected} />
}

export const Default: Story = {
  render: Template,
  args: {
    name: 'metodos',
    options,
    values: ['pix'],
  },
}

export const WithError: Story = {
  render: Template,
  args: {
    name: 'metodos',
    options,
    values: [],
    errorMessage: 'Selecione pelo menos um método',
  },
}

export const WithDisabledOption: Story = {
  render: Template,
  args: {
    name: 'metodos',
    options: [
      ...options,
      { value: 'desabilitado', label: 'Desabilitado', disabled: true },
    ],
    values: ['boleto'],
  },
}
