import type { Meta, StoryObj } from '@storybook/react-vite'
import { RadioGroup } from '@thrift/design-system/packages/molecules/RadioGroup'
import { useState } from 'react'

export default {
  title: 'Molecules/RadioGroup',
  component: RadioGroup,
} satisfies Meta<typeof RadioGroup>

type Story = StoryObj<typeof RadioGroup>

const options = [
  { value: 'pix', label: 'Pix', description: 'Receba instantaneamente' },
  { value: 'boleto', label: 'Boleto', description: 'Até 3 dias úteis' },
  { value: 'transferencia', label: 'Transferência', description: 'Até 24h' },
]

const Template = (args: React.ComponentProps<typeof RadioGroup>) => {
  const [selected, setSelected] = useState<string>(args.value || '')

  return <RadioGroup {...args} value={selected} onChange={setSelected} />
}

export const Default: Story = {
  render: Template,
  args: {
    name: 'pagamento',
    value: 'pix',
    options,
  },
}

export const WithError: Story = {
  render: Template,
  args: {
    name: 'pagamento',
    value: '',
    options,
    errorMessage: 'Selecione uma opção',
  },
}

export const DisabledOption: Story = {
  render: Template,
  args: {
    name: 'pagamento',
    value: 'pix',
    options: [
      ...options.slice(0, 2),
      {
        value: 'transferencia',
        label: 'Transferência',
        description: 'Até 24h',
        disabled: true,
      },
    ],
  },
}
