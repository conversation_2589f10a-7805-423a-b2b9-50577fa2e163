import {
  radioGroupItemVariants,
  type RadioGroupProps,
} from '@thrift/design-system/packages/molecules/RadioGroup'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  options,
  value,
  onChange,
  errorMessage,
  disabled = false,
}) => {
  const hasError = Boolean(errorMessage)

  return (
    <div className="flex flex-col gap-2 w-full">
      {options.map((option) => {
        const selected = value === option.value
        const isDisabled = disabled || option.disabled

        return (
          <label
            key={option.value}
            className={radioGroupItemVariants({
              disabled: isDisabled,
            })}
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={selected}
              onChange={() => onChange?.(option.value)}
              disabled={option.disabled}
              className="mt-1.5 h-[1.6rem] w-[1.6rem] accent-secondary cursor-pointer"
            />

            <div>
              {option.label && (
                <Typography weight="bold">{option.label}</Typography>
              )}
              {option.description && (
                <Typography className="text-gray-900">
                  {option.description}
                </Typography>
              )}
            </div>
          </label>
        )
      })}

      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
