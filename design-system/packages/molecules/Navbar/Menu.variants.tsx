import { tv, type VariantProps } from 'tailwind-variants'

export const menuVariants = tv({
  base: 'fixed left-0 h-screen w-full md:w-200 lg:w-200 bg-gray-200 !pb-tiny z-50 overflow-y-auto transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:block md:translate-x-0 md:static md:block',
  variants: {
    open: {
      true: 'translate-x-0',
      false: '-translate-x-full',
    },
  },
  defaultVariants: {
    open: false,
  },
})

export type MenuVariantProps = VariantProps<typeof menuVariants>
