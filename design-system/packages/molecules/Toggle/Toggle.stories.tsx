import type { Meta, StoryObj } from '@storybook/react-vite'
import { Toggle } from '@thrift/design-system/packages/molecules/Toggle'
import { useState } from 'react'

export default {
  title: 'Molecules/Toggle',
  component: Toggle,
} satisfies Meta<typeof Toggle>

type Story = StoryObj<typeof Toggle>

const Template = (args: React.ComponentProps<typeof Toggle>) => {
  const [checked, setChecked] = useState(args.checked ?? false)

  return <Toggle {...args} checked={checked} onChange={setChecked} />
}

export const Default: Story = {
  render: Template,
  args: {
    name: 'toggle-default',
    label: 'Label',
    description: 'Description',
    checked: false,
  },
}

export const Checked: Story = {
  render: Template,
  args: {
    name: 'toggle-checked',
    label: 'Label',
    description: 'Ativado por padrão',
    checked: true,
  },
}

export const Disabled: Story = {
  render: Template,
  args: {
    name: 'toggle-disabled',
    label: 'Label',
    description: 'N<PERSON> pode ser alterado',
    checked: true,
    disabled: true,
  },
}
