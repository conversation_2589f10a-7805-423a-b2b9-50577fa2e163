import { tv } from 'tailwind-variants'

export const toggleVariants = tv({
  base: 'flex items-center justify-between !p-tiny !rounded-tiny cursor-pointer transition-colors w-full',
  variants: {
    disabled: {
      true: 'opacity-50 cursor-not-allowed',
    },
  },
  defaultVariants: {
    disabled: false,
  },
})

export const toggleItemContainerVariants = tv({
  base: 'block w-[4rem] h-[2.6rem] rounded-full transition',
  variants: {
    checked: {
      true: '!bg-secondary',
      false: '!bg-gray-300',
    },
  },
  defaultVariants: {
    checked: false,
  },
})

export const toggleItemCircleVariants = tv({
  base: 'absolute top-[0.2rem] left-[0.2rem] w-[2.4rem] h-[2.2rem] bg-white flex items-center justify-center rounded-full transition transform',
  variants: {
    checked: {
      true: 'top-[0.2rem] translate-x-[1.2rem]',
    },
  },
  defaultVariants: {
    checked: false,
  },
})
