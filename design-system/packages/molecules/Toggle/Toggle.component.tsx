import {
  toggleItemContainerVariants,
  toggleItemCircleVariants,
  toggleVariants,
  type ToggleProps,
} from '@thrift/design-system/packages/molecules/Toggle'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const Toggle: React.FC<ToggleProps> = ({
  name,
  label,
  description,
  checked = false,
  disabled = false,
  onChange,
}) => {
  return (
    <label
      htmlFor={name}
      className={toggleVariants({
        disabled,
      })}
    >
      <div className="flex flex-col">
        {label && <Typography weight="bold">{label}</Typography>}
        {description && (
          <Typography className="text-gray-900">{description}</Typography>
        )}
      </div>

      <div className="relative inline-block w-[4rem] h-[2.6rem]">
        <input
          id={name}
          name={name}
          type="checkbox"
          className="sr-only"
          checked={checked}
          disabled={disabled}
          onChange={(e) => onChange?.(e.target.checked)}
        />
        <div
          className={toggleItemContainerVariants({
            checked,
          })}
        />
        <div
          className={toggleItemCircleVariants({
            checked,
          })}
        />
      </div>
    </label>
  )
}
