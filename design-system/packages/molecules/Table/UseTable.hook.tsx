import { Loader } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

export function useTable<T>(
  data: T[],
  onEndReached?: () => void,
  labelEmpty?: string,
  isLoading?: boolean,
  onCheckAll?: (checked: boolean) => void,
  onCheckRow?: (row: T, checked: boolean) => void,
) {
  const [checkedRows, setCheckedRows] = useState<number[]>([])
  const [checkAll, setCheckAll] = useState(false)
  const loadMoreRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting && typeof onEndReached === 'function') {
        onEndReached()
      }
    })

    if (loadMoreRef.current) observer.observe(loadMoreRef.current)

    return () => observer.disconnect()
  }, [onEndReached])

  const handleCheckAll = (checked: boolean) => {
    setCheckAll(checked)
    if (onCheckAll) onCheckAll(checked)
    setCheckedRows(checked ? data.map((_, i) => i) : [])
  }

  const handleCheckRow = (index: number, checked: boolean) => {
    const newChecked = checked
      ? [...checkedRows, index]
      : checkedRows.filter((i) => i !== index)

    setCheckedRows(newChecked)
    if (onCheckRow) onCheckRow(data[index], checked)
  }

  const renderStatus = (): React.ReactNode => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center p-tiny text-gray-900">
          <Loader className="h-5 w-5 animate-spin" />
        </div>
      )
    }

    if (data.length === 0) {
      return (
        <div className="p-4 text-center text-gray-900 text-tiny">
          {labelEmpty}
        </div>
      )
    }

    return undefined
  }

  return {
    checkedRows,
    checkAll,
    loadMoreRef,
    handleCheckAll,
    handleCheckRow,
    renderStatus,
  }
}
