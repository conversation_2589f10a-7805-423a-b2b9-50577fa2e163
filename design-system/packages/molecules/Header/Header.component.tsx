import type { HeaderProps } from '@thrift/design-system/packages/molecules/Header'
import { UserMenu } from '@thrift/design-system/packages/molecules/UserMenu'
import {
  Logo,
  LogoModeOptions,
  LogoVariantOptions,
} from '@thrift/design-system/packages/molecules/Logo'

export const Header: React.FC<HeaderProps> = ({ userMenu }) => {
  return (
    <header className="fixed top-0 left-0 w-full h-[6rem] bg-primary flex items-center justify-between px-tiny shadow z-50">
      <a href="/">
        <Logo
          variant={LogoVariantOptions.ICON_FILL}
          mode={LogoModeOptions.DARK}
          size="h-[6rem] w-[6rem]"
        />
      </a>

      <UserMenu {...userMenu} />
    </header>
  )
}
