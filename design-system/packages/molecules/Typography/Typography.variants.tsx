import { tv, type VariantProps } from 'tailwind-variants'

export const typographVariants = tv({
  variants: {
    variant: {
      h1: '!text-huge',
      h2: '!text-large',
      h3: '!text-medium',
      h4: '!text-small',
      h5: '!text-tiny',
      p: '!text-tiny',
      label: '!text-tiny',
    },
    weight: {
      regular: 'font-normal',
      semibold: 'font-semibold',
      bold: 'font-bold',
    },
    align: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
    },
  },
  defaultVariants: {
    weight: 'regular',
    align: 'left',
  },
})

export type TypographVariantProps = VariantProps<typeof typographVariants>
