import {
  typographVariants,
  type TypographyProps,
} from '@thrift/design-system/packages/molecules/Typography'
import type { JSX } from 'react'
import classNames from 'classnames'

export const Typography: React.FC<TypographyProps> = ({
  variant = 'p',
  weight = 'regular',
  children,
  className,
  htmlFor,
}) => {
  const Tag = variant as keyof JSX.IntrinsicElements

  const sharedProps = {
    className: classNames(className, typographVariants({ variant, weight })),
  }

  if (Tag === 'label') {
    return (
      <label htmlFor={htmlFor} {...sharedProps}>
        {children}
      </label>
    )
  }

  return <Tag {...sharedProps}>{children}</Tag>
}
