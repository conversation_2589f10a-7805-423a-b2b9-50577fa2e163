import type { Meta, StoryObj } from '@storybook/react-vite'
import {
  type TypographyProps,
  Typography,
} from '@thrift/design-system/packages/molecules/Typography'

const meta = {
  title: 'Molecules/Typography',
  component: Typography,
} satisfies Meta<TypographyProps>

export default meta

type Story = StoryObj<TypographyProps>

type VariantType = 'h1' | 'h2' | 'h3' | 'h4' | 'h5'

const variants = [
  { label: 'Thrift Technology', variant: 'h1' },
  { label: 'Thrift Technology', variant: 'h2' },
  { label: 'Thrift Technology', variant: 'h3' },
  { label: 'Thrift Technology', variant: 'h4' },
  { label: 'Thrift Technology', variant: 'h5' },
]

export const Default: Story = {
  render: () => (
    <div className="flex w-full flex-col mx-50">
      <>
        <Typography variant={'h1'} weight="regular">
          Regular
        </Typography>
        <hr className="text-gray-200" />
        <div className="border border-gray-200 rounded-md p-4 space-y-3 font-sans mb-8">
          {variants.map(({ variant, label }, index) => (
            <div key={index} className="flex items-center gap-4">
              <Typography variant={'h5'} weight="regular">
                {variant}
              </Typography>
              <Typography variant={variant as VariantType}>{label}</Typography>
            </div>
          ))}
        </div>
      </>
      <>
        <Typography variant={'h1'} weight="semibold">
          Semi bold
        </Typography>
        <hr className="text-gray-200" />
        <div className="border border-gray-200 rounded-md p-4 space-y-3 font-sans mb-8">
          {variants.map(({ variant, label }, index) => (
            <div key={index} className="flex items-center gap-4">
              <Typography variant={'h5'} weight="regular">
                {variant}
              </Typography>
              <Typography variant={variant as VariantType} weight="semibold">
                {label}
              </Typography>
            </div>
          ))}
        </div>
      </>
      <>
        <Typography variant={'h1'} weight="bold">
          Bold
        </Typography>
        <hr className="text-gray-200" />
        <div className="border border-gray-200 rounded-md p-4 space-y-3 font-sans mb-8">
          {variants.map(({ variant, label }, index) => (
            <div key={index} className="flex items-center gap-4">
              <Typography variant={'h5'} weight="regular">
                {variant}
              </Typography>
              <Typography variant={variant as VariantType} weight="bold">
                {label}
              </Typography>
            </div>
          ))}
        </div>
      </>
    </div>
  ),
}
