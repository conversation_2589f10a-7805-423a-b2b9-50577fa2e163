import React from 'react'
import { AddressForm } from '@thrift/design-system/packages/molecules/AddressForm/AddressForm.component'
import {
  addressFormSchema,
  type AddressFormSchemaType,
} from '@thrift/design-system/packages/molecules/AddressForm/AddressForm.schemas'

import type { Meta, StoryObj } from '@storybook/react'

const cityOptions = [
  { label: 'São Paulo', value: 'sao-paulo' },
  { label: 'Rio de Janeiro', value: 'rio-de-janeiro' },
]

const regionOptions = [
  { label: 'SP', value: 'sp' },
  { label: 'RJ', value: 'rj' },
]

const defaultValue: AddressFormSchemaType = {
  postalCode: '',
  line1: '',
  number: '',
  line2: '',
  neighborhood: '',
  city: '',
  region: '',
}

const meta: Meta<typeof AddressForm> = {
  title: 'Molecules/AddressForm',
  component: AddressForm,
  argTypes: {
    value: { control: 'object' },
  },
  args: {
    value: defaultValue,
  },
}

export default meta

type Story = StoryObj<typeof AddressForm>

const ControlsComponent: React.FC<{ value: AddressFormSchemaType }> = (
  args,
) => {
  const [value, setValue] = React.useState<AddressFormSchemaType>(args.value)

  const [errors, setErrors] = React.useState<
    Partial<Record<keyof AddressFormSchemaType, string>>
  >({})

  React.useEffect(() => {
    setValue(args.value)
  }, [args.value])

  const handleChange = (next: AddressFormSchemaType) => {
    setValue(next)

    const result = addressFormSchema.safeParse(next)

    if (!result.success) {
      const fieldErrors: Partial<Record<keyof AddressFormSchemaType, string>> =
        {}

      result.error.errors.forEach((err) => {
        if (err.path[0]) {
          fieldErrors[err.path[0] as keyof AddressFormSchemaType] = err.message
        }
      })

      setErrors(fieldErrors)
    } else {
      setErrors({})
    }
  }

  return (
    <div className="max-w-2xl mx-auto mt-8">
      <AddressForm
        {...args}
        value={value}
        onChange={handleChange}
        cityOptions={cityOptions}
        regionOptions={regionOptions}
        errors={errors}
      />
    </div>
  )
}

export const Controls: Story = {
  render: (args) => <ControlsComponent {...args} />,
}
