import { z } from 'zod'

export const addressFormSchema = z.object({
  postalCode: z.string().min(1, 'CEP is required'),
  line1: z.string().min(1, 'Street is required'),
  number: z.string().min(1, 'Number is required'),
  line2: z.string().optional(),
  neighborhood: z.string().min(1, 'Neighborhood is required'),
  city: z.string().min(1, 'City is required'),
  region: z.string().min(1, 'State is required'),
})

export type AddressFormSchemaType = z.infer<typeof addressFormSchema>
