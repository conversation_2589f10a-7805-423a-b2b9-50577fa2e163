import { useCallback, useOptimistic } from 'react'
import { InputCEP } from '@thrift/design-system/packages/molecules/InputCEP'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { Select } from '@thrift/design-system/packages/molecules/Select'
import type { AddressFormProps as BaseAddressFormProps } from '@thrift/design-system/packages/molecules/AddressForm/AddressForm.types'
import type { AddressFormSchemaType } from '@thrift/design-system/packages/molecules/AddressForm/AddressForm.schemas'

type AddressFormLabels = {
  postalCode?: string
  line1?: string
  line2?: string
  neighborhood?: string
  region?: string
  city?: string
}

type AddressFormPlaceholders = {
  postalCode?: string
  line1?: string
  line2?: string
  neighborhood?: string
  region?: string
  city?: string
}

type AddressFormProps = BaseAddressFormProps & {
  onFieldChange?: (field: keyof AddressFormSchemaType, value: string) => void
  labels?: AddressFormLabels
  placeholders?: AddressFormPlaceholders
}

function useAddressForm(
  initialValue: AddressFormSchemaType,
  onChange: (v: AddressFormSchemaType) => void,
  onFieldChange?: (field: keyof AddressFormSchemaType, value: string) => void,
) {
  const [optimisticValue, setOptimisticValue] = useOptimistic(initialValue)

  const handleFieldChange = useCallback(
    (field: keyof AddressFormSchemaType, fieldValue: string) => {
      if (onFieldChange) {
        onFieldChange(field, fieldValue)

        return
      }
      const next = { ...optimisticValue, [field]: fieldValue }

      setOptimisticValue(next)
      onChange(next)
    },
    [onChange, onFieldChange, optimisticValue, setOptimisticValue],
  )

  return { value: optimisticValue, handleFieldChange }
}

// eslint-disable-next-line complexity
export const AddressForm: React.FC<AddressFormProps> = ({
  value: initialValue,
  onChange,
  cityOptions,
  regionOptions,
  errors = {},
  disabled = false,
  required = false,
  onFieldChange,
  labels = {},
  placeholders = {},
}) => {
  const {
    postalCode: postalCodeLabel = 'CEP',
    line1: line1Label = 'Rua',
    line2: line2Label = 'Complemento',
    neighborhood: neighborhoodLabel = 'Bairro',
    region: regionLabel = 'Estado',
    city: cityLabel = 'Cidade',
  } = labels

  const {
    postalCode: postalCodePlaceholder = '99999-999',
    line1: line1Placeholder = '',
    line2: line2Placeholder = '',
    neighborhood: neighborhoodPlaceholder = '',
    region: regionPlaceholder = 'Selecione um estado...',
    city: cityPlaceholder = 'Selecione uma cidade...',
  } = placeholders

  const { value, handleFieldChange } = useAddressForm(
    initialValue,
    onChange,
    onFieldChange,
  )

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div className="flex flex-col gap-4">
        <div>
          <InputCEP
            label={postalCodeLabel}
            value={value.postalCode}
            onChange={(text) => handleFieldChange('postalCode', text)}
            errorMessage={errors.postalCode}
            disabled={disabled}
            required={required}
            placeholder={postalCodePlaceholder}
          />
        </div>
        <div>
          <InputText
            label={line1Label}
            value={value.line1}
            onChange={(e) => handleFieldChange('line1', e.target.value)}
            errorMessage={errors.line1}
            disabled={disabled}
            required={required}
            placeholder={line1Placeholder}
          />
        </div>
        <div>
          <InputText
            label={line2Label}
            value={value.line2}
            onChange={(e) => handleFieldChange('line2', e.target.value)}
            errorMessage={errors.line2}
            disabled={disabled}
            required={required}
            placeholder={line2Placeholder}
          />
        </div>
        <div>
          <InputText
            label={neighborhoodLabel}
            value={value.neighborhood}
            onChange={(e) => handleFieldChange('neighborhood', e.target.value)}
            errorMessage={errors.neighborhood}
            disabled={disabled}
            required={required}
            placeholder={neighborhoodPlaceholder}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Select
            label={regionLabel}
            value={value.region}
            onChange={(val) => handleFieldChange('region', val)}
            options={regionOptions}
            errorMessage={errors.region}
            disabled={disabled}
            placeholder={regionPlaceholder}
          />
          <Select
            label={cityLabel}
            value={value.city}
            onChange={(val) => handleFieldChange('city', val)}
            options={cityOptions}
            errorMessage={errors.city}
            disabled={disabled}
            placeholder={cityPlaceholder}
          />
        </div>
      </div>
    </div>
  )
}
