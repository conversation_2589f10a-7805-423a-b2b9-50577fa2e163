import type { AddressFormSchemaType } from '@thrift/design-system/packages/molecules/AddressForm/AddressForm.schemas'

export type AddressFormProps = {
  value: AddressFormSchemaType
  onChange: (value: AddressFormSchemaType) => void
  cityOptions: { label: string; value: string }[]
  regionOptions: { label: string; value: string }[]
  errors?: Partial<Record<keyof AddressFormSchemaType, string>>
  disabled?: boolean
  required?: boolean
}

export type UseAddressFormParams = {
  initialValue: AddressFormSchemaType
  onChange: (v: AddressFormSchemaType) => void
}

export type UseAddressFormReturn = {
  value: AddressFormSchemaType
  handleFieldChange: (
    field: keyof AddressFormSchemaType,
    fieldValue: string,
  ) => void
}
