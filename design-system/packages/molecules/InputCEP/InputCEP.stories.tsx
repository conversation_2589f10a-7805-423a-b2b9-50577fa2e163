import type { Meta } from '@storybook/react-vite'
import { InputCEP } from '@thrift/design-system/packages/molecules/InputCEP'
import React from 'react'

export default {
  title: 'Molecules/InputCEP',
  component: InputCEP,
} satisfies Meta<typeof InputCEP>

export const Default = () => {
  const [text, setText] = React.useState('')

  return (
    <div className="w-72">
      <InputCEP
        label="Cep"
        value={text}
        onChange={(t) => setText(t as string)}
        name="cep"
      />
    </div>
  )
}

export const WithError = () => (
  <div className="w-72">
    <InputCEP
      label="Cep"
      value=""
      onChange={() => ({})}
      errorMessage="Campo obrigatório"
    />
  </div>
)

export const Disabled = () => (
  <div className="w-72">
    <InputCEP label="Cep" value="13300-520" onChange={() => ({})} disabled />
  </div>
)
