import {
  inputCEPVariants,
  type InputCEPProps,
} from '@thrift/design-system/packages/molecules/InputCEP'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const InputCEP = ({
  label,
  name,
  errorMessage,
  placeholder = '',
  disabled = false,
  onChange,
  ...rest
}: InputCEPProps) => {
  const hasError = Boolean(errorMessage)

  const formatCEP = (cep: string) => {
    const cepFormatted = cep.replace(/\D/g, '')

    return cepFormatted.replace(/^(\d{5})(\d)/, '$1-$2').slice(0, 9)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value

    const masked = formatCEP(raw)

    onChange?.(masked)
  }

  return (
    <div className="flex flex-col gap-1">
      <Typography variant="label" weight="bold" htmlFor={name}>
        {label}
      </Typography>
      <input
        id={name}
        name={name}
        placeholder={placeholder}
        className={inputCEPVariants({
          error: hasError,
          disabled,
        })}
        onChange={handleChange}
        disabled={disabled}
        {...rest}
      />
      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
