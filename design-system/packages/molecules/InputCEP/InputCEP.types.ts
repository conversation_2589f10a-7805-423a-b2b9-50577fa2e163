import type { InputHTMLAttributes } from 'react'

export type InputCEPProps = {
  label?: string
  name?: string
  errorMessage?: string
  placeholder?: string
  disabled?: boolean
  onChange?: (text: string) => void
} & Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'>

type ValueType<T> = {
  value: T
  message: string
}

export type InputCEPSchemaProps = {
  required: ValueType<boolean>
  onlyNumbersErrorMessage?: string
  invalidLengthErrorMessage?: string
}
