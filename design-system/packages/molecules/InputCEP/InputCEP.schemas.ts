import { z } from 'zod'
import type { InputCEPSchemaProps } from '@thrift/design-system/packages/molecules/InputCEP'

export const InputCEPSchema = ({
  required,
  onlyNumbersErrorMessage,
  invalidLengthErrorMessage,
}: InputCEPSchemaProps) => {
  const schema = z.string()

  if (required?.value) {
    schema.nonempty(required.message)
  }

  schema
    .refine((value) => value !== 'nill', {
      message: required.message,
    })
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return replacedValue.length <= 8
    }, invalidLengthErrorMessage)
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return !!Number(replacedValue)
    }, onlyNumbersErrorMessage)

  return schema
}
