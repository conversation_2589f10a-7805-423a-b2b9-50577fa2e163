import type { MoreActionsProps } from '@thrift/design-system/packages/molecules/MoreActions'
import { useEffect, useRef, useState } from 'react'
import { MoreVertical } from 'lucide-react'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const MoreActions = ({ options }: MoreActionsProps) => {
  const [open, setOpen] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)

    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className="relative inline-block text-left" ref={ref}>
      <button
        onClick={() => setOpen((prev) => !prev)}
        className="p-tiny hover:bg-gray-100 rounded"
      >
        <MoreVertical className="w-8 h-8 text-black" />
      </button>

      {open && (
        <div className="absolute right-0 z-10 mt-2 origin-top-right rounded-tiny bg-white border border-gray-200 shadow-lg">
          <ul className="text-sm text-black">
            {options.map((option, i) => (
              <li
                key={`${option.label}-${i}`}
                className="px-tiny py-4 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  option.onClick()
                  setOpen(false)
                }}
              >
                <Typography weight="regular">{option.label}</Typography>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}
