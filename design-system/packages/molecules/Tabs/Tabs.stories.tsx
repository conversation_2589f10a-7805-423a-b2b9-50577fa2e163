import { useState } from 'react'
import type { Meta, StoryObj } from '@storybook/react'
import {
  Tabs,
  type TabsProps,
} from '@thrift/design-system/packages/molecules/Tabs'

const meta: Meta<typeof Tabs> = {
  title: 'Molecules/Tabs',
  component: Tabs,
  argTypes: {
    tabs: {
      control: 'object',
      description: 'Lista de abas com label e conteúdo',
    },
  },
}

export default meta

type Story = StoryObj<typeof Tabs>

const Template = (args: TabsProps) => {
  const [activeIndex, setActiveIndex] = useState(0)

  return <Tabs {...args} activeIndex={activeIndex} onChange={setActiveIndex} />
}

export const Default: Story = {
  render: Template,
  args: {
    tabs: [
      { label: 'Home', content: <div>🏠 Conteúdo da Home</div> },
      { label: 'Perfil', content: <div>👤 Conteúdo do Perfil</div> },
      {
        label: 'Configurações',
        content: <div>⚙️ Conteúdo das Configurações</div>,
      },
    ],
  },
}
