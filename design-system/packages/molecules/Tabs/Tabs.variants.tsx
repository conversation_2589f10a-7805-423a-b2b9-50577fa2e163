import { tv, type VariantProps } from 'tailwind-variants'

export const tabsVariants = tv({
  base: 'pb-2 whitespace-nowrap transition-colors',
  variants: {
    isActive: {
      true: 'text-primary border-b-2 border-primary',
      false: 'text-gray-900 hover:text-primary border-b-2 border-transparent',
    },
  },
  defaultVariants: {
    isActive: false,
  },
})

export type TabsVariantProps = VariantProps<typeof tabsVariants>
