import {
  type TabsProps,
  tabsVariants,
} from '@thrift/design-system/packages/molecules/Tabs'
import { useEffect, useRef } from 'react'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const Tabs = ({ activeIndex, onChange, tabs }: TabsProps) => {
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([])

  useEffect(() => {
    const activeTab = tabRefs.current[activeIndex]

    if (activeTab) {
      activeTab.scrollIntoView({ behavior: 'smooth', inline: 'center' })
    }
  }, [activeIndex])

  return (
    <div className="w-full">
      <div className="overflow-x-auto hide-scrollbar">
        <div className="inline-flex w-max space-x-6 border-b border-gray-200">
          {tabs.map((tab, index) => {
            const isActive = index === activeIndex

            return (
              <button
                key={`${tab.label}-${index}`}
                ref={(el) => {
                  tabRefs.current[index] = el
                }}
                onClick={() => onChange(index)}
                className={tabsVariants({ isActive })}
              >
                <Typography weight={isActive ? 'bold' : 'regular'}>
                  {tab.label}
                </Typography>
              </button>
            )
          })}
        </div>
      </div>

      <div className="py-tiny">{tabs[activeIndex]?.content}</div>
    </div>
  )
}
