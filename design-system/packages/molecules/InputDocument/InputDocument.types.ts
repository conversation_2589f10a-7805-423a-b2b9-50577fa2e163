import type { InputHTMLAttributes } from 'react'

export type InputDocumentProps = {
  label?: string
  name?: string
  errorMessage?: string
  placeholder?: string
  disabled?: boolean
  onChange?: (text: string) => void
  type: 'CPF' | 'CNPJ'
} & InputHTMLAttributes<HTMLInputElement>

type ValueType<T> = {
  value: T
  message: string
}

export type InputDocumentSchemaProps = {
  required: ValueType<boolean>
  onlyNumbersErrorMessage?: string
  invalidLengthErrorMessage?: string
}
