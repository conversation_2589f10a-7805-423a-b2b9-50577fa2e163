import type { Meta } from '@storybook/react-vite'
import { InputDocument } from '@thrift/design-system/packages/molecules/InputDocument'
import React from 'react'

export default {
  title: 'Molecules/InputDocument',
  component: InputDocument,
} satisfies Meta<typeof InputDocument>

export const Default = () => {
  const [cpf, setCPF] = React.useState('')
  const [cnpj, setCNPJ] = React.useState('')

  return (
    <div className="w-72 gap-4">
      <InputDocument
        type="CPF"
        label="Cpf"
        value={cpf}
        onChange={(t) => setCPF(t as string)}
        name="cpf"
      />

      <InputDocument
        type="CNPJ"
        label="Cnpj"
        value={cnpj}
        onChange={(t) => setCNPJ(t as string)}
        name="Cnpj"
      />
    </div>
  )
}

export const WithError = () => (
  <div className="w-72 gap-4">
    <InputDocument
      type="CPF"
      label="Cpf"
      value=""
      onChange={() => ({})}
      errorMessage="Campo obrigatório"
    />
    <InputDocument
      type="CNPJ"
      label="Cnpj"
      value=""
      onChange={() => ({})}
      errorMessage="Campo obrigatório"
    />
  </div>
)

export const Disabled = () => (
  <div className="w-72 gap-4">
    <InputDocument
      type="CPF"
      label="Cpf"
      value="333.333.333-33"
      onChange={() => ({})}
      disabled
    />
    <InputDocument
      type="CNPJ"
      label="Cnpj"
      value="33.333.333/0001-33"
      onChange={() => ({})}
      disabled
    />
  </div>
)
