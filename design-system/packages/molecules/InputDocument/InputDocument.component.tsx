import {
  inputDocumentVariants,
  type InputDocumentProps,
} from '@thrift/design-system/packages/molecules/InputDocument'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const InputDocument = ({
  label,
  name,
  errorMessage,
  placeholder = '',
  disabled = false,
  onChange,
  type,
  ...rest
}: InputDocumentProps) => {
  const hasError = <PERSON>olean(errorMessage)

  const formatValue = (value: string) => {
    const cleaned = value.replace(/\D/g, '')

    if (type === 'CPF') {
      return cleaned
        .replace(/^(\d{3})(\d)/, '$1.$2')
        .replace(/^(\d{3})\.(\d{3})(\d)/, '$1.$2.$3')
        .replace(/\.(\d{3})(\d)/, '.$1-$2')
        .slice(0, 14)
    }

    if (type === 'CNPJ') {
      return cleaned
        .replace(/^(\d{2})(\d)/, '$1.$2')
        .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
        .replace(/\.(\d{3})(\d)/, '.$1/$2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .slice(0, 18)
    }

    return value
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value
    const masked = formatValue(raw)

    onChange?.(masked)
  }

  return (
    <div className="flex flex-col gap-1">
      <Typography variant="label" weight="bold" htmlFor={name}>
        {label}
      </Typography>
      <input
        id={name}
        name={name}
        placeholder={placeholder}
        className={inputDocumentVariants({
          error: hasError,
          disabled,
        })}
        onChange={handleChange}
        {...rest}
      />
      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
