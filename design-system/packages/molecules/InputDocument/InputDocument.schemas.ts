import { z } from 'zod'
import type { InputDocumentSchemaProps } from '@thrift/design-system/packages/molecules/InputDocument'

export const InputCPFSchema = ({
  required,
  invalidLengthErrorMessage,
  onlyNumbersErrorMessage,
}: InputDocumentSchemaProps) => {
  const schema = z.string()

  if (required?.value) {
    schema.nonempty(required.message)
  }

  schema
    .nonempty({ message: required.message })
    .refine((value) => value !== 'nill', {
      message: required.message,
    })
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return replacedValue.length <= 11
    }, invalidLengthErrorMessage)
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return !!Number(replacedValue)
    }, onlyNumbersErrorMessage)

  return schema
}

export const InputCNPJSchema = ({
  required,
  invalidLengthErrorMessage,
  onlyNumbersErrorMessage,
}: InputDocumentSchemaProps) => {
  const schema = z.string()

  if (required?.value) {
    schema.nonempty(required.message)
  }

  schema
    .refine((value) => value !== 'nill', {
      message: required.message,
    })
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return replacedValue.length <= 14
    }, invalidLengthErrorMessage)
    .refine((value) => {
      const replacedValue = value.replace(/\D/g, '')

      return !!Number(replacedValue)
    }, onlyNumbersErrorMessage)

  return schema
}
