import type { Meta } from '@storybook/react-vite'
import { InputTextArea } from '@thrift/design-system/packages/molecules/InputTextArea'
import React from 'react'

export default {
  title: 'Molecules/InputTextArea',
  component: InputTextArea,
} satisfies Meta<typeof InputTextArea>

export const Default = () => {
  const [text, setText] = React.useState('')

  return (
    <div className="w-72">
      <InputTextArea
        label="Comentário"
        value={text}
        onChange={(e) => setText(e.target.value)}
        name="comentario"
      />
    </div>
  )
}

export const WithError = () => (
  <div className="w-72">
    <InputTextArea
      label="Comentário"
      value=""
      onChange={() => ({})}
      errorMessage="Campo obrigatório"
    />
  </div>
)

export const Disabled = () => (
  <div className="w-72">
    <InputTextArea
      label="Comentário"
      value="Daniel Antunes"
      onChange={() => ({})}
      disabled
    />
  </div>
)
