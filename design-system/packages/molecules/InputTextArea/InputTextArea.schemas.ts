import { z } from 'zod'
import type { InputTextAreaSchemaProps } from '@thrift/design-system/packages/molecules/InputTextArea'

export const InputTextAreaSchema = ({
  max,
  min,
  required,
}: InputTextAreaSchemaProps) => {
  const schema = z
    .string()
    .min(min.value, { message: min.message })
    .max(max.value, { message: max.message })

  if (required?.value) {
    schema.nonempty(required.message)
  }

  return schema
}
