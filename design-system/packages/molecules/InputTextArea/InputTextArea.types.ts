import type { TextareaHTMLAttributes } from 'react'

export type InputTextAreaProps = {
  label?: string
  name?: string
  errorMessage?: string
  placeholder?: string
  disabled?: boolean
} & TextareaHTMLAttributes<HTMLTextAreaElement>

type ValueType<T> = {
  value: T
  message: string
}

export type InputTextAreaSchemaProps = {
  min: ValueType<number>
  max: ValueType<number>
  required: ValueType<boolean>
}
