import {
  inputTextAreaVariants,
  type InputTextAreaProps,
} from '@thrift/design-system/packages/molecules/InputTextArea'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const InputTextArea = ({
  label,
  name,
  errorMessage,
  placeholder = '',
  disabled = false,
  ...rest
}: InputTextAreaProps) => {
  const hasError = Boolean(errorMessage)

  return (
    <div className="flex flex-col gap-1">
      <Typography variant="label" weight="bold" htmlFor={name}>
        {label}
      </Typography>
      <textarea
        id={name}
        name={name}
        placeholder={placeholder}
        className={inputTextAreaVariants({
          error: hasError,
          disabled,
        })}
        disabled={disabled}
        {...rest}
      />
      {hasError && <Typography className="text-red">{errorMessage}</Typography>}
    </div>
  )
}
