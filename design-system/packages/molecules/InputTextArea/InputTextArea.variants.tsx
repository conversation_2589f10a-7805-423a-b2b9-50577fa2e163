import { tv } from 'tailwind-variants'

export const inputTextAreaVariants = tv({
  base: 'w-full min-h-[10rem] !text-tiny px-[1.6rem] py-2 border !rounded-tiny outline-none transition-colors appearance-none text-black',
  variants: {
    error: {
      true: '!border-red !text-red',
      false: 'border-gray-300',
    },
    disabled: {
      true: 'bg-gray-100 text-gray-400 cursor-not-allowed',
    },
  },
  defaultVariants: {
    error: false,
  },
})
