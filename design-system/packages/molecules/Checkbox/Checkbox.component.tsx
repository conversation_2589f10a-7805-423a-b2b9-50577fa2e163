import {
  checkboxVariants,
  type CheckboxProps,
} from '@thrift/design-system/packages/molecules/Checkbox'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const Checkbox: React.FC<CheckboxProps> = ({
  name,
  value,
  label,
  description,
  checked = false,
  disabled = false,
  onChange,
}) => {
  return (
    <label
      className={checkboxVariants({
        disabled,
      })}
    >
      <input
        type="checkbox"
        name={name}
        value={value}
        checked={checked}
        disabled={disabled}
        onChange={(e) => onChange?.(e.target.checked)}
        className="mt-1.5 h-[1.6rem] w-[1.6rem] accent-secondary cursor-pointer"
      />

      <div>
        {label && <Typography weight="bold">{label}</Typography>}
        {description && (
          <Typography className="text-gray-900">{description}</Typography>
        )}
      </div>
    </label>
  )
}
