import type { Meta, StoryObj } from '@storybook/react-vite'
import { Checkbox } from '@thrift/design-system/packages/molecules/Checkbox'

export default {
  title: 'Molecules/Checkbox',
  component: Checkbox,
} satisfies Meta<typeof Checkbox>

type Story = StoryObj<typeof Checkbox>

export const Default: Story = {
  args: {
    name: 'termos',
    value: 'aceito',
    label: 'Aceito os termos de uso',
    description: 'Você precisa aceitar para continuar',
    checked: false,
  },
}

export const Checked: Story = {
  args: {
    name: 'termos',
    value: 'aceito',
    label: 'Aceito os termos de uso',
    description: 'Você precisa aceitar para continuar',
    checked: true,
  },
}

export const Disabled: Story = {
  args: {
    name: 'termos',
    value: 'aceito',
    label: 'Desabilitado',
    description: 'Esta opção está desabilitada',
    checked: false,
    disabled: true,
  },
}
