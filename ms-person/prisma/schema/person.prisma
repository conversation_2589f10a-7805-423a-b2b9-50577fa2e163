model Person {
  id            String         @id @default(uuid())
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  deleted       <PERSON><PERSON><PERSON>        @default(false)
  PersonNatural PersonNatural?
  PersonLegal   PersonLegal?
  Document      Document?
  Address       Address?
  Contact       Contact?
  StoreCredits  StoreCredit[]

  @@map("persons")
}
