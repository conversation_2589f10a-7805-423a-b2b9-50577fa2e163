-- CreateTable
CREATE TABLE `store_credits` (
    `id` VARCHAR(191) NOT NULL,
    `personId` VARCHAR(191) NOT NULL,
    `balance` DECIMAL(65, 30) NOT NULL DEFAULT 0,
    `currency` VARCHAR(191) NOT NULL DEFAULT 'BRL',
    `issuingStoreLegalPersonId` VARCHAR(191) NOT NULL,
    `originatingProposalId` VARCHAR(191) NOT NULL,
    `notes` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deleted` BOOLEAN NOT NULL DEFAULT false,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `store_credits` ADD CONSTRAINT `store_credits_personId_fkey` FOREIGN KEY (`personId`) REFERENCES `persons`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `store_credits` ADD CONSTRAINT `store_credits_issuingStoreLegalPersonId_fkey` FOREIGN KEY (`issuingStoreLegalPersonId`) REFERENCES `person_legals`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
