{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "Person Microservice API", "description": "API for managing person data, addresses, contacts, and store credits", "contact": {"name": "Thrift Technology", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080/person", "description": "Development server"}], "tags": [{"name": "Person", "description": "Person management operations"}, {"name": "PersonNatural", "description": "Natural person operations"}, {"name": "PersonLegal", "description": "Legal person operations"}, {"name": "Address", "description": "Address management operations"}, {"name": "Contact", "description": "Contact management operations"}, {"name": "StoreCredit", "description": "Store credit operations"}], "components": {"schemas": {"UUID": {"type": "string", "format": "uuid", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "example": "123e4567-e89b-12d3-a456-************"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000Z"}, "PersonNaturalBase": {"type": "object", "properties": {"firstName": {"type": "string", "minLength": 1}, "lastName": {"type": "string", "minLength": 1}}, "description": "Base schema for natural person"}, "CreatePersonNatural": {"type": "object", "properties": {"firstName": {"type": "string", "minLength": 1}, "lastName": {"type": "string", "minLength": 1}}, "description": "Schema for creating a natural person"}, "UpdatePersonNatural": {"type": "object", "properties": {"firstName": {"type": "string", "minLength": 1}, "lastName": {"type": "string", "minLength": 1}, "id": {"type": "string", "format": "uuid"}, "personId": {"type": "string", "format": "uuid"}}, "required": ["id", "personId"], "description": "Schema for updating a natural person"}, "FilterPersonNatural": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "search": {"type": "string"}, "page": {"anyOf": [{"type": "string"}, {"type": "number"}]}, "perPage": {"anyOf": [{"type": "string"}, {"type": "number"}]}, "sortBy": {"type": "string"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"]}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}, "description": "Schema for filtering natural persons"}, "StoreCreditBase": {"type": "object", "properties": {"personId": {"type": "string", "format": "uuid"}, "balance": {"type": "number", "minimum": 0}, "currency": {"type": "string", "default": "BRL"}, "issuingStoreLegalPersonId": {"type": "string", "format": "uuid"}, "originatingProposalId": {"type": "string", "format": "uuid"}, "notes": {"type": "string"}}, "required": ["personId", "balance", "issuingStoreLegalPersonId", "originatingProposalId"], "description": "Base schema for store credit"}, "CreateStoreCredit": {"type": "object", "properties": {"personId": {"type": "string", "format": "uuid"}, "balance": {"type": "number", "minimum": 0}, "currency": {"type": "string", "default": "BRL"}, "issuingStoreLegalPersonId": {"type": "string", "format": "uuid"}, "originatingProposalId": {"type": "string", "format": "uuid"}, "notes": {"type": "string"}}, "required": ["personId", "balance", "issuingStoreLegalPersonId", "originatingProposalId"], "description": "Schema for creating store credit"}, "UpdateStoreCredit": {"type": "object", "properties": {"personId": {"type": "string", "format": "uuid"}, "balance": {"type": "number", "minimum": 0}, "currency": {"type": "string", "default": "BRL"}, "issuingStoreLegalPersonId": {"type": "string", "format": "uuid"}, "originatingProposalId": {"type": "string", "format": "uuid"}, "notes": {"type": "string"}, "id": {"type": "string", "format": "uuid"}}, "required": ["id"], "description": "Schema for updating store credit"}, "PersonResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "createdAt", "updatedAt"], "description": "Basic person response object"}, "PersonNaturalResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "firstName": {"type": "string", "description": "First name of the natural person", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name of the natural person", "example": "<PERSON><PERSON>"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "createdAt", "updatedAt"], "description": "Natural person response object"}, "PersonLegalResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "fictitiousName": {"type": "string", "description": "Fictitious name of the legal person", "example": "Acme Corp"}, "legalName": {"type": "string", "description": "Legal name of the legal person", "example": "Acme Corporation Ltd."}, "nickname": {"type": "string", "description": "Nickname of the legal person", "example": "Acme"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "fictitiousName", "legalName", "nickname", "createdAt", "updatedAt"], "description": "Legal person response object"}, "AddressResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "line1": {"type": "string", "description": "Primary address line", "example": "123 Main Street"}, "number": {"type": "string", "description": "Address number", "example": "123"}, "line2": {"type": "string", "description": "Secondary address line", "example": "Apt 4B"}, "city": {"type": "string", "description": "City name", "example": "São Paulo"}, "region": {"type": "string", "description": "State or region", "example": "SP"}, "country": {"type": "string", "description": "Country code", "example": "BR"}, "postalCode": {"type": "string", "description": "Postal code", "example": "01234-567"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "line1", "number", "line2", "city", "region", "country", "postalCode", "createdAt", "updatedAt"], "description": "Address response object"}, "ContactResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "label": {"type": "string", "description": "Contact label", "example": "Primary Email"}, "type": {"type": "string", "description": "Contact type", "example": "email"}, "value": {"type": "string", "description": "Contact value", "example": "<EMAIL>"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "label", "type", "value", "createdAt", "updatedAt"], "description": "Contact response object"}, "StoreCreditResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "balance": {"type": "number", "minimum": 0, "multipleOf": 0.01, "description": "Store credit balance amount", "example": 100.5}, "currency": {"type": "string", "description": "Currency code", "example": "BRL"}, "issuingStoreLegalPersonId": {"$ref": "#/components/schemas/UUID"}, "originatingProposalId": {"$ref": "#/components/schemas/UUID"}, "notes": {"type": "string", "description": "Additional notes", "example": "Credit issued for returned item"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "balance", "currency", "issuingStoreLegalPersonId", "originatingProposalId", "createdAt", "updatedAt"], "description": "Store credit response object"}, "ApiResponsePerson": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "createdAt", "updatedAt"], "description": "Person response object"}}, "required": ["code", "message"]}, "ApiResponsePersonNatural": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "createdAt", "updatedAt"], "description": "Natural person response object"}}, "required": ["code", "message"]}, "ApiResponsePersonLegal": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "fictitiousName": {"type": "string"}, "legalName": {"type": "string"}, "nickname": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "fictitiousName", "legalName", "nickname", "createdAt", "updatedAt"], "description": "Legal person response object"}}, "required": ["code", "message"]}, "ApiResponseAddress": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "line1": {"type": "string"}, "number": {"type": "string"}, "line2": {"type": "string"}, "city": {"type": "string"}, "region": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "line1", "number", "line2", "city", "region", "country", "postalCode", "createdAt", "updatedAt"], "description": "Address response object"}}, "required": ["code", "message"]}, "ApiResponseContact": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "label": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "label", "type", "value", "createdAt", "updatedAt"], "description": "Contact response object"}}, "required": ["code", "message"]}, "ApiResponseStoreCredit": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "balance": {"type": "number"}, "currency": {"type": "string"}, "issuingStoreLegalPersonId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "originatingProposalId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "notes": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "balance", "currency", "issuingStoreLegalPersonId", "originatingProposalId", "createdAt", "updatedAt"], "description": "Store credit response object"}}, "required": ["code", "message"]}, "ApiResponsePersonArray": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "createdAt", "updatedAt"], "description": "Person response object"}}}, "required": ["code", "message"]}, "ApiResponsePersonNaturalArray": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "createdAt", "updatedAt"], "description": "Natural person response object"}}}, "required": ["code", "message"]}, "ApiResponsePersonLegalArray": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "fictitiousName": {"type": "string"}, "legalName": {"type": "string"}, "nickname": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "fictitiousName", "legalName", "nickname", "createdAt", "updatedAt"], "description": "Legal person response object"}}}, "required": ["code", "message"]}, "ApiResponseAddressArray": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "line1": {"type": "string"}, "number": {"type": "string"}, "line2": {"type": "string"}, "city": {"type": "string"}, "region": {"type": "string"}, "country": {"type": "string"}, "postalCode": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "line1", "number", "line2", "city", "region", "country", "postalCode", "createdAt", "updatedAt"], "description": "Address response object"}}}, "required": ["code", "message"]}, "ApiResponseContactArray": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "label": {"type": "string"}, "type": {"type": "string"}, "value": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "label", "type", "value", "createdAt", "updatedAt"], "description": "Contact response object"}}}, "required": ["code", "message"]}, "ApiResponseStoreCreditArray": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "personId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "balance": {"type": "number"}, "currency": {"type": "string"}, "issuingStoreLegalPersonId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "originatingProposalId": {"type": "string", "format": "uuid", "description": "UUID identifier", "example": "123e4567-e89b-12d3-a456-************"}, "notes": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp", "example": "2024-01-15T10:30:00.000Z"}}, "required": ["id", "personId", "balance", "currency", "issuingStoreLegalPersonId", "originatingProposalId", "createdAt", "updatedAt"], "description": "Store credit response object"}}}, "required": ["code", "message"]}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number for pagination"}, "perPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Number of items per page"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "asc", "description": "Sort order"}, "search": {"type": "string", "description": "Search term for filtering results"}}}, "PaginationMeta": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "perPage": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "required": ["page", "perPage", "total", "totalPages", "hasNextPage", "hasPreviousPage"]}, "PaginatedResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}, "required": ["data", "meta"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "Error code identifier"}, "message": {"type": "string", "description": "Human-readable error message"}, "data": {"type": "string", "nullable": true, "description": "Additional error data"}}, "required": ["code", "message"]}, "ValidationError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_400_0400"}, "message": {"type": "string", "example": "Validation failed"}, "data": {"type": "object", "properties": {"field": {"type": "string", "description": "Field that failed validation"}, "value": {"description": "Value that failed validation"}, "constraint": {"type": "string", "description": "Validation constraint that was violated"}}}}, "required": ["code", "message"]}, "NotFoundError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_404_0404"}, "message": {"type": "string", "example": "Resource not found"}, "data": {"type": "string", "nullable": true}}, "required": ["code", "message"]}, "ConflictError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_409_0409"}, "message": {"type": "string", "example": "Resource already exists"}, "data": {"type": "string", "nullable": true}}, "required": ["code", "message"]}, "InternalServerError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_500_0500"}, "message": {"type": "string", "example": "Internal server error"}, "data": {"type": "string", "nullable": true}}, "required": ["code", "message"]}, "CreatePersonNaturalRequest": {"type": "object", "properties": {"firstName": {"type": "string", "minLength": 1, "description": "First name of the natural person", "example": "<PERSON>"}, "lastName": {"type": "string", "minLength": 1, "description": "Last name of the natural person", "example": "<PERSON><PERSON>"}}, "description": "Request body for creating a natural person"}, "UpdatePersonNaturalRequest": {"type": "object", "properties": {"firstName": {"type": "string", "minLength": 1, "description": "First name of the natural person", "example": "<PERSON>"}, "lastName": {"type": "string", "minLength": 1, "description": "Last name of the natural person", "example": "<PERSON><PERSON>"}}, "description": "Request body for updating a natural person"}, "CreatePersonLegalRequest": {"type": "object", "properties": {"fictitiousName": {"type": "string", "minLength": 1, "description": "Fictitious name of the legal person", "example": "Acme Corp"}, "legalName": {"type": "string", "minLength": 1, "description": "Legal name of the legal person", "example": "Acme Corporation Ltd."}, "nickname": {"type": "string", "minLength": 1, "description": "Nickname of the legal person", "example": "Acme"}}, "required": ["fictitiousName", "legalName", "nickname"], "description": "Request body for creating a legal person"}, "CreateAddressRequest": {"type": "object", "properties": {"personId": {"$ref": "#/components/schemas/UUID", "description": "ID of the person this address belongs to"}, "line1": {"type": "string", "minLength": 1, "description": "Primary address line", "example": "123 Main Street"}, "number": {"type": "string", "minLength": 1, "description": "Address number", "example": "123"}, "line2": {"type": "string", "description": "Secondary address line (apartment, suite, etc.)", "example": "Apt 4B"}, "city": {"type": "string", "minLength": 1, "description": "City name", "example": "São Paulo"}, "region": {"type": "string", "minLength": 1, "description": "State or region", "example": "SP"}, "country": {"type": "string", "minLength": 1, "description": "Country code", "example": "BR"}, "postalCode": {"type": "string", "minLength": 1, "description": "Postal code", "example": "01234-567"}}, "required": ["personId", "line1", "number", "line2", "city", "region", "country", "postalCode"], "description": "Request body for creating an address"}, "CreateContactRequest": {"type": "object", "properties": {"personId": {"$ref": "#/components/schemas/UUID", "description": "ID of the person this contact belongs to"}, "label": {"type": "string", "minLength": 1, "description": "Contact label", "example": "Primary Email"}, "type": {"type": "string", "minLength": 1, "description": "Contact type", "example": "email", "enum": ["email", "phone", "mobile", "fax", "website"]}, "value": {"type": "string", "minLength": 1, "description": "Contact value", "example": "<EMAIL>"}}, "required": ["personId", "label", "type", "value"], "description": "Request body for creating a contact"}, "CreateStoreCreditRequest": {"type": "object", "properties": {"personId": {"$ref": "#/components/schemas/UUID", "description": "ID of the person receiving the store credit"}, "balance": {"type": "number", "minimum": 0, "multipleOf": 0.01, "description": "Store credit balance amount", "example": 100.5}, "currency": {"type": "string", "default": "BRL", "description": "Currency code", "example": "BRL"}, "issuingStoreLegalPersonId": {"$ref": "#/components/schemas/UUID", "description": "ID of the legal person (store) issuing the credit"}, "originatingProposalId": {"$ref": "#/components/schemas/UUID", "description": "ID of the proposal that originated this store credit"}, "notes": {"type": "string", "description": "Additional notes about the store credit", "example": "Credit issued for returned item"}}, "required": ["personId", "balance", "issuingStoreLegalPersonId", "originatingProposalId"], "description": "Request body for creating store credit"}, "UpdateStoreCreditRequest": {"type": "object", "properties": {"balance": {"type": "number", "minimum": 0, "multipleOf": 0.01, "description": "Store credit balance amount", "example": 100.5}, "currency": {"type": "string", "description": "Currency code", "example": "BRL"}, "notes": {"type": "string", "description": "Additional notes about the store credit", "example": "Credit updated due to partial usage"}}, "description": "Request body for updating store credit"}, "StandardApiResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "Response code identifier", "example": "C_200_0200"}, "message": {"type": "string", "description": "Human-readable response message", "example": "Operation completed successfully"}, "data": {"description": "Response data (varies by endpoint)"}}, "required": ["code", "message"]}}, "parameters": {}}, "paths": {"/person": {"get": {"tags": ["Person"], "summary": "Get all persons", "description": "Retrieve a list of all persons", "responses": {"200": {"description": "List of persons retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponsePersonArray"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/person/{personId}": {"get": {"tags": ["Person"], "summary": "Get person by ID", "description": "Retrieve a specific person by their ID", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "Person ID", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "description": "Person ID", "name": "personId", "in": "path"}], "responses": {"200": {"description": "Person retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponsePerson"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/person-natural": {"post": {"tags": ["PersonNatural"], "summary": "Create natural person", "description": "Create a new natural person", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePersonNatural"}}}}, "responses": {"201": {"description": "Natural person created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponsePersonNatural"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "get": {"tags": ["PersonNatural"], "summary": "Get all natural persons", "description": "Retrieve a list of all natural persons with optional filtering", "parameters": [{"schema": {"type": "integer", "minimum": 1}, "required": false, "name": "page", "in": "query"}, {"schema": {"type": "integer", "minimum": 1, "maximum": 100}, "required": false, "name": "perPage", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "search", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "firstName", "in": "query"}, {"schema": {"type": "string"}, "required": false, "name": "lastName", "in": "query"}], "responses": {"200": {"description": "List of natural persons retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponsePersonNaturalArray"}}}}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/person-natural/{id}": {"get": {"tags": ["PersonNatural"], "summary": "Get natural person by ID", "description": "Retrieve a specific natural person by their ID", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "Natural person ID", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "description": "Natural person ID", "name": "id", "in": "path"}], "responses": {"200": {"description": "Natural person retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponsePersonNatural"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "put": {"tags": ["PersonNatural"], "summary": "Update natural person", "description": "Update an existing natural person", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "Natural person ID", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "description": "Natural person ID", "name": "id", "in": "path"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePersonNatural"}}}}, "responses": {"200": {"description": "Natural person updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponsePersonNatural"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}, "delete": {"tags": ["PersonNatural"], "summary": "Delete natural person", "description": "Delete an existing natural person", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "Natural person ID", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "description": "Natural person ID", "name": "id", "in": "path"}], "responses": {"204": {"description": "Natural person deleted successfully"}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/person-legal": {"post": {"tags": ["PersonLegal"], "summary": "Create legal person", "description": "Create a new legal person", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"fictitiousName": {"type": "string"}, "legalName": {"type": "string"}, "nickname": {"type": "string"}}, "required": ["fictitiousName", "legalName", "nickname"]}}}}, "responses": {"201": {"description": "Legal person created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponsePersonLegal"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/address/{id}": {"get": {"tags": ["Address"], "summary": "Get address by ID", "description": "Retrieve a specific address by its ID", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "Address ID", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "description": "Address ID", "name": "id", "in": "path"}], "responses": {"200": {"description": "Address retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseAddress"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/contact/{id}": {"get": {"tags": ["Contact"], "summary": "Get contact by ID", "description": "Retrieve a specific contact by its ID", "parameters": [{"schema": {"type": "string", "format": "uuid", "description": "Contact ID", "example": "123e4567-e89b-12d3-a456-************"}, "required": true, "description": "Contact ID", "name": "id", "in": "path"}], "responses": {"200": {"description": "Contact retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseContact"}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/store-credits": {"post": {"tags": ["StoreCredit"], "summary": "Create store credit", "description": "Create a new store credit record", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStoreCredit"}}}}, "responses": {"201": {"description": "Store credit created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponseStoreCredit"}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}}