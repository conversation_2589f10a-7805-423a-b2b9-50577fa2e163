{"components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "Error code identifier"}, "message": {"type": "string", "description": "Human-readable error message"}, "data": {"type": "string", "nullable": true, "description": "Additional error data"}}, "required": ["code", "message"]}, "ValidationError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_400_0400"}, "message": {"type": "string", "example": "Validation failed"}, "data": {"type": "object", "properties": {"field": {"type": "string", "description": "Field that failed validation"}, "value": {"description": "Value that failed validation"}, "constraint": {"type": "string", "description": "Validation constraint that was violated"}}}}, "required": ["code", "message"]}, "NotFoundError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_404_0404"}, "message": {"type": "string", "example": "Resource not found"}, "data": {"type": "string", "nullable": true}}, "required": ["code", "message"]}, "ConflictError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_409_0409"}, "message": {"type": "string", "example": "Resource already exists"}, "data": {"type": "string", "nullable": true}}, "required": ["code", "message"]}, "InternalServerError": {"type": "object", "properties": {"code": {"type": "string", "example": "C_500_0500"}, "message": {"type": "string", "example": "Internal server error"}, "data": {"type": "string", "nullable": true}}, "required": ["code", "message"]}}, "responses": {"BadRequest": {"description": "Bad Request - Invalid input data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}, "NotFound": {"description": "Resource not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundError"}}}}, "Conflict": {"description": "Resource already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConflictError"}}}}, "InternalServerError": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerError"}}}}}}}