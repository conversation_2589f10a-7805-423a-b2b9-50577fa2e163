{"components": {"schemas": {"StandardApiResponse": {"type": "object", "properties": {"code": {"type": "string", "description": "Response code identifier", "example": "C_200_0200"}, "message": {"type": "string", "description": "Human-readable response message", "example": "Operation completed successfully"}, "data": {"description": "Response data (varies by endpoint)"}}, "required": ["code", "message"]}, "PersonResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "createdAt", "updatedAt"], "description": "Basic person response object"}, "PersonNaturalResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "firstName": {"type": "string", "description": "First name of the natural person", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name of the natural person", "example": "<PERSON><PERSON>"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "createdAt", "updatedAt"], "description": "Natural person response object"}, "PersonLegalResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "fictitiousName": {"type": "string", "description": "Fictitious name of the legal person", "example": "Acme Corp"}, "legalName": {"type": "string", "description": "Legal name of the legal person", "example": "Acme Corporation Ltd."}, "nickname": {"type": "string", "description": "Nickname of the legal person", "example": "Acme"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "fictitiousName", "legalName", "nickname", "createdAt", "updatedAt"], "description": "Legal person response object"}, "AddressResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "line1": {"type": "string", "description": "Primary address line", "example": "123 Main Street"}, "number": {"type": "string", "description": "Address number", "example": "123"}, "line2": {"type": "string", "description": "Secondary address line", "example": "Apt 4B"}, "city": {"type": "string", "description": "City name", "example": "São Paulo"}, "region": {"type": "string", "description": "State or region", "example": "SP"}, "country": {"type": "string", "description": "Country code", "example": "BR"}, "postalCode": {"type": "string", "description": "Postal code", "example": "01234-567"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "line1", "number", "line2", "city", "region", "country", "postalCode", "createdAt", "updatedAt"], "description": "Address response object"}, "ContactResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "label": {"type": "string", "description": "Contact label", "example": "Primary Email"}, "type": {"type": "string", "description": "Contact type", "example": "email"}, "value": {"type": "string", "description": "Contact value", "example": "<EMAIL>"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "label", "type", "value", "createdAt", "updatedAt"], "description": "Contact response object"}, "StoreCreditResponse": {"type": "object", "properties": {"id": {"$ref": "#/components/schemas/UUID"}, "personId": {"$ref": "#/components/schemas/UUID"}, "balance": {"type": "number", "minimum": 0, "multipleOf": 0.01, "description": "Store credit balance amount", "example": 100.5}, "currency": {"type": "string", "description": "Currency code", "example": "BRL"}, "issuingStoreLegalPersonId": {"$ref": "#/components/schemas/UUID"}, "originatingProposalId": {"$ref": "#/components/schemas/UUID"}, "notes": {"type": "string", "description": "Additional notes", "example": "Credit issued for returned item"}, "createdAt": {"$ref": "#/components/schemas/Timestamp"}, "updatedAt": {"$ref": "#/components/schemas/Timestamp"}}, "required": ["id", "personId", "balance", "currency", "issuingStoreLegalPersonId", "originatingProposalId", "createdAt", "updatedAt"], "description": "Store credit response object"}}}}