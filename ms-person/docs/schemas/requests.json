{"components": {"schemas": {"CreatePersonNaturalRequest": {"type": "object", "properties": {"firstName": {"type": "string", "minLength": 1, "description": "First name of the natural person", "example": "<PERSON>"}, "lastName": {"type": "string", "minLength": 1, "description": "Last name of the natural person", "example": "<PERSON><PERSON>"}}, "description": "Request body for creating a natural person"}, "UpdatePersonNaturalRequest": {"type": "object", "properties": {"firstName": {"type": "string", "minLength": 1, "description": "First name of the natural person", "example": "<PERSON>"}, "lastName": {"type": "string", "minLength": 1, "description": "Last name of the natural person", "example": "<PERSON><PERSON>"}}, "description": "Request body for updating a natural person"}, "CreatePersonLegalRequest": {"type": "object", "properties": {"fictitiousName": {"type": "string", "minLength": 1, "description": "Fictitious name of the legal person", "example": "Acme Corp"}, "legalName": {"type": "string", "minLength": 1, "description": "Legal name of the legal person", "example": "Acme Corporation Ltd."}, "nickname": {"type": "string", "minLength": 1, "description": "Nickname of the legal person", "example": "Acme"}}, "required": ["fictitiousName", "legalName", "nickname"], "description": "Request body for creating a legal person"}, "CreateAddressRequest": {"type": "object", "properties": {"personId": {"$ref": "#/components/schemas/UUID", "description": "ID of the person this address belongs to"}, "line1": {"type": "string", "minLength": 1, "description": "Primary address line", "example": "123 Main Street"}, "number": {"type": "string", "minLength": 1, "description": "Address number", "example": "123"}, "line2": {"type": "string", "description": "Secondary address line (apartment, suite, etc.)", "example": "Apt 4B"}, "city": {"type": "string", "minLength": 1, "description": "City name", "example": "São Paulo"}, "region": {"type": "string", "minLength": 1, "description": "State or region", "example": "SP"}, "country": {"type": "string", "minLength": 1, "description": "Country code", "example": "BR"}, "postalCode": {"type": "string", "minLength": 1, "description": "Postal code", "example": "01234-567"}}, "required": ["personId", "line1", "number", "line2", "city", "region", "country", "postalCode"], "description": "Request body for creating an address"}, "CreateContactRequest": {"type": "object", "properties": {"personId": {"$ref": "#/components/schemas/UUID", "description": "ID of the person this contact belongs to"}, "label": {"type": "string", "minLength": 1, "description": "Contact label", "example": "Primary Email"}, "type": {"type": "string", "minLength": 1, "description": "Contact type", "example": "email", "enum": ["email", "phone", "mobile", "fax", "website"]}, "value": {"type": "string", "minLength": 1, "description": "Contact value", "example": "<EMAIL>"}}, "required": ["personId", "label", "type", "value"], "description": "Request body for creating a contact"}, "CreateStoreCreditRequest": {"type": "object", "properties": {"personId": {"$ref": "#/components/schemas/UUID", "description": "ID of the person receiving the store credit"}, "balance": {"type": "number", "minimum": 0, "multipleOf": 0.01, "description": "Store credit balance amount", "example": 100.5}, "currency": {"type": "string", "default": "BRL", "description": "Currency code", "example": "BRL"}, "issuingStoreLegalPersonId": {"$ref": "#/components/schemas/UUID", "description": "ID of the legal person (store) issuing the credit"}, "originatingProposalId": {"$ref": "#/components/schemas/UUID", "description": "ID of the proposal that originated this store credit"}, "notes": {"type": "string", "description": "Additional notes about the store credit", "example": "Credit issued for returned item"}}, "required": ["personId", "balance", "issuingStoreLegalPersonId", "originatingProposalId"], "description": "Request body for creating store credit"}, "UpdateStoreCreditRequest": {"type": "object", "properties": {"balance": {"type": "number", "minimum": 0, "multipleOf": 0.01, "description": "Store credit balance amount", "example": 100.5}, "currency": {"type": "string", "description": "Currency code", "example": "BRL"}, "notes": {"type": "string", "description": "Additional notes about the store credit", "example": "Credit updated due to partial usage"}}, "description": "Request body for updating store credit"}}}}