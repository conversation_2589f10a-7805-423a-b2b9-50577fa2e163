{"components": {"schemas": {"UUID": {"type": "string", "format": "uuid", "pattern": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", "example": "123e4567-e89b-12d3-a456-************"}, "Timestamp": {"type": "string", "format": "date-time", "example": "2024-01-15T10:30:00.000Z"}, "PaginationQuery": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number for pagination"}, "perPage": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Number of items per page"}, "sortBy": {"type": "string", "description": "Field to sort by"}, "sortOrder": {"type": "string", "enum": ["asc", "desc"], "default": "asc", "description": "Sort order"}, "search": {"type": "string", "description": "Search term for filtering results"}}}, "PaginationMeta": {"type": "object", "properties": {"page": {"type": "integer", "minimum": 1}, "perPage": {"type": "integer", "minimum": 1}, "total": {"type": "integer", "minimum": 0}, "totalPages": {"type": "integer", "minimum": 0}, "hasNextPage": {"type": "boolean"}, "hasPreviousPage": {"type": "boolean"}}, "required": ["page", "perPage", "total", "totalPages", "hasNextPage", "hasPreviousPage"]}, "PaginatedResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {}}, "meta": {"$ref": "#/components/schemas/PaginationMeta"}}, "required": ["data", "meta"]}}}}