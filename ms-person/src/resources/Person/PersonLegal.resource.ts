import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Delete, Get, Post, Put } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  type IdentifierDto,
  PersonLegal as PersonLegalApplication,
  type PersonLegalResponse,
  type PersonRequest,
} from '@app/application/Person'

export class Person {
  @Post('/person-legal')
  public async create({ request, response }: ResourceMethodProps) {
    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_201_0201}`,
        ),
        data: await new PersonLegalApplication().create(request.body()),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }

  @Get('/person-legal')
  public async findAll({ response }: ResourceMethodProps) {
    try {
      response.send<PersonLegalResponse[]>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().findAll(),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }

  @Get('/person-legal/:id')
  public async findById({ request, response }: ResourceMethodProps) {
    const { id } = request.params<IdentifierDto>()

    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().findById(id),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }

  @Get('/person-legal/personId/:personId')
  public async findByPersonId({ request, response }: ResourceMethodProps) {
    const { personId } = request.params<PersonRequest>()

    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().findByPersonId(personId),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }

  @Put('/person-legal/:personId')
  public async update({ request, response }: ResourceMethodProps) {
    const { personId } = request.params<PersonRequest>()

    try {
      response.send<PersonLegalResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().update({
          ...request.body(),
          id: personId,
        }),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }

  @Delete('/person-legal/:id')
  public async delete({ request, response }: ResourceMethodProps) {
    const { id } = request.params<IdentifierDto>()

    try {
      response.send({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonLegalApplication().delete(id),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }
}
