import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Get } from '@thrift/common/engines/Server'

import { Language } from '@app/application/Language'
import {
  Person as PersonApplication,
  type PersonRequest,
  type PersonResponse,
} from '@app/application/Person'

export class Person {
  @Get('/person')
  public async findAll({ response }: ResourceMethodProps) {
    try {
      response.send<PersonResponse[]>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonApplication().findAll(),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }

  @Get('/person/:personId')
  public async findById({ request, response }: ResourceMethodProps) {
    try {
      response.send<PersonResponse>({
        code: ResourceMessageCode.C_200_0200,
        message: Language.translate(
          `resource:${ResourceMessageCode.C_200_0200}`,
        ),
        data: await new PersonApplication().findById(
          request.params<PersonRequest>().personId,
        ),
      })
    } catch (error) {
      ResponseHelper.handleError(error, response, 'person', Language.translate)
    }
  }
}
