export type PersonModel = {
  id: string
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type PersonLegalModel = {
  id: string
  personId: string
  fictitiousName: string
  legalName: string
  nickname: string
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type PersonNaturalModel = {
  id: string
  personId: string
  firstName?: string | null
  lastName?: string | null
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type PersonCreateProps = Omit<
  PersonModel,
  'id' | 'createdAt' | 'updatedAt' | 'deleted'
>

export type PersonUpdateProps = Omit<
  PersonModel,
  'createdAt' | 'updatedAt' | 'deleted'
>

export type PersonLegalCreateProps = Omit<
  PersonLegalModel,
  'id' | 'personId' | 'createdAt' | 'updatedAt' | 'deleted'
>

export type PersonLegalUpdateProps = Omit<
  PersonLegalModel,
  'createdAt' | 'updatedAt' | 'deleted'
>

export type PersonNaturalCreateProps = Omit<
  PersonNaturalModel,
  'id' | 'personId' | 'createdAt' | 'updatedAt' | 'deleted'
>

export type PersonNaturalUpdateProps = Omit<
  PersonNaturalModel,
  'createdAt' | 'updatedAt' | 'deleted'
>

export type PersonRequest = {
  personId: string
}

export type IdentifierDto = {
  id: string
}

export type PersonResponse = {
  id: string
  createdAt: Date
  updatedAt: Date
}

export type PersonLegalResponse = {
  id: string
  personId: string
  fictitiousName: string
  legalName: string
  nickname: string
  createdAt: Date
  updatedAt: Date
}

export type PersonNaturalResponse = {
  id: string
  personId: string
  displayName: string
  firstName?: string | null
  lastName?: string | null
  createdAt: Date
  updatedAt: Date
}
