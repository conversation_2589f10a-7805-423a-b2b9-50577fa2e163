import { Language } from '@app/application/Language'
import type {
  PersonCreateProps,
  PersonResponse,
  PersonUpdateProps,
} from '@app/application/Person'

import { Person as PersonDatabase } from '@app/domain/database/Person'

export class Person {
  public async create(props: PersonCreateProps = {}): Promise<PersonResponse> {
    const model = new PersonDatabase()

    const person = await model.create(props)

    return person
  }

  public async findAll(): Promise<PersonResponse[]> {
    const model = new PersonDatabase()

    const person = await model.findAll()

    return person
  }

  public async findById(personId: string): Promise<PersonResponse> {
    const model = new PersonDatabase()

    const { id, createdAt, updatedAt } = await model.findById(personId)

    return { id, createdAt, updatedAt }
  }

  public async update({ id }: PersonUpdateProps): Promise<PersonResponse> {
    const model = new PersonDatabase()

    const person = await model.update({ id })

    return person
  }

  public async delete(id: string) {
    const model = new PersonDatabase()

    const existingItem = await model.findById(id)

    if (!existingItem) {
      throw new ReferenceError(Language.translate('person:notFound', { id }))
    }

    await model.delete(id)
  }
}
