import { Language } from '@app/application/Language'
import {
  Person,
  type PersonLegalCreateProps,
  type PersonLegalResponse,
  type PersonLegalUpdateProps,
} from '@app/application/Person'

import { PersonLegal as PersonLegalDatabase } from '@app/domain/database/PersonLegal'

export class PersonLegal {
  public async create({
    nickname,
    fictitiousName,
    legalName,
  }: PersonLegalCreateProps): Promise<PersonLegalResponse> {
    const personApp = new Person()

    const person = await personApp.create({})

    const model = new PersonLegalDatabase()

    const personLegal = await model.create({
      fictitiousName,
      legalName,
      personId: person.id,
    })

    return { ...personLegal, nickname }
  }

  public async findAll(): Promise<PersonLegalResponse[]> {
    const model = new PersonLegalDatabase()

    const personLegal = await model.findAll()

    return personLegal as unknown as PersonLegalResponse[]
  }

  public async findById(findId: string): Promise<PersonLegalResponse> {
    const model = new PersonLegalDatabase()

    const { id, personId, fictitiousName, legalName, createdAt, updatedAt } =
      await model.findById(findId)

    return {
      id,
      personId,
      fictitiousName,
      legalName,
      createdAt,
      updatedAt,
    } as unknown as PersonLegalResponse
  }

  public async findByPersonId(
    findPersonId: string,
  ): Promise<Omit<PersonLegalResponse, 'deleted'>> {
    const model = new PersonLegalDatabase()

    const { id, personId, fictitiousName, legalName, createdAt, updatedAt } =
      await model.findByPersonId(findPersonId)

    return {
      id,
      personId,
      fictitiousName,
      legalName,
      createdAt,
      updatedAt,
    } as unknown as PersonLegalResponse
  }

  public async update({
    id,
    fictitiousName,
    legalName,
    personId,
    nickname,
  }: PersonLegalUpdateProps): Promise<PersonLegalResponse> {
    const model = new PersonLegalDatabase()

    const personLegal = await model.update({
      id,
      fictitiousName,
      legalName,
      personId,
    })

    return { ...personLegal, nickname }
  }

  public async delete(id: string) {
    const model = new PersonLegalDatabase()

    const existingItem = await model.findById(id)

    if (!existingItem) {
      throw new ReferenceError(Language.translate('person:notFound', { id }))
    }

    const personApp = new Person()

    await personApp.delete(existingItem.personId)

    return await model.delete(id)
  }
}
