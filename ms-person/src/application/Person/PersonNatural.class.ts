import { Language } from '@app/application/Language'
import { Person, type PersonNaturalResponse } from '@app/application/Person'
import {
  CreatePersonNaturalSchema,
  PersonNaturalBaseSchema,
  UpdatePersonNaturalSchema,
} from '@app/application/Person/PersonNatural.schema'

import { PersonNatural as PersonNaturalDatabase } from '@app/domain/database/PersonNatural'

export class PersonNatural {
  private generateDisplayName(
    firstName?: string | null,
    lastName?: string | null,
  ): string {
    const first = firstName || ''
    const last = lastName || ''

    return `${first} ${last}`.trim() || 'Unknown Person'
  }
  public async create(input: unknown): Promise<PersonNaturalResponse> {
    const dto = CreatePersonNaturalSchema.parse(input)

    const model = new PersonNaturalDatabase()
    const personApp = new Person()

    const person = await personApp.create({})

    const personNatural = await model.create({
      firstName: dto.firstName,
      lastName: dto.lastName,
      personId: person.id,
    })

    return {
      ...personNatural,
      displayName: this.generateDisplayName(dto.firstName, dto.lastName),
    }
  }

  public async findAll(): Promise<PersonNaturalResponse[]> {
    const model = new PersonNaturalDatabase()

    const personNatural = await model.findAll()

    return personNatural.map((person) => ({
      ...person,
      displayName: this.generateDisplayName(person.firstName, person.lastName),
    }))
  }

  public async findById(findId: string): Promise<PersonNaturalResponse> {
    const model = new PersonNaturalDatabase()

    const { id, personId, firstName, lastName, createdAt, updatedAt } =
      await model.findById(findId)

    return {
      id,
      personId,
      firstName,
      lastName,
      displayName: this.generateDisplayName(firstName, lastName),
      createdAt,
      updatedAt,
    }
  }

  public async findByPersonId(
    findPersonId: string,
  ): Promise<Omit<PersonNaturalResponse, 'deleted'>> {
    const model = new PersonNaturalDatabase()

    const { id, personId, firstName, lastName, createdAt, updatedAt } =
      await model.findByPersonId(findPersonId)

    return {
      id,
      personId,
      firstName,
      lastName,
      displayName: this.generateDisplayName(firstName, lastName),
      createdAt,
      updatedAt,
    }
  }

  public async update(input: unknown): Promise<PersonNaturalResponse> {
    const dto = UpdatePersonNaturalSchema.parse(input)

    const model = new PersonNaturalDatabase()

    // Get existing record to merge with updates
    const existingRecord = await model.findById(dto.id)

    const personNatural = await model.update({
      id: dto.id,
      firstName: dto.firstName ?? existingRecord.firstName,
      lastName: dto.lastName ?? existingRecord.lastName,
      personId: dto.personId ?? existingRecord.personId,
    })

    return {
      ...personNatural,
      displayName: this.generateDisplayName(
        personNatural.firstName,
        personNatural.lastName,
      ),
    }
  }

  public async updateByPersonId(
    input: unknown,
  ): Promise<PersonNaturalResponse> {
    const dto = PersonNaturalBaseSchema.parse(input)

    const model = new PersonNaturalDatabase()

    // Get existing record by personId
    const inputData = input as { personId?: string; id?: string }
    const personId = inputData.personId || inputData.id

    if (!personId) {
      throw new ReferenceError('PersonId is required')
    }

    const existingRecord = await model.findByPersonId(personId)

    const personNatural = await model.update({
      id: existingRecord.id,
      firstName: dto.firstName ?? existingRecord.firstName,
      lastName: dto.lastName ?? existingRecord.lastName,
      personId: existingRecord.personId,
    })

    return {
      ...personNatural,
      displayName: this.generateDisplayName(
        personNatural.firstName,
        personNatural.lastName,
      ),
    }
  }

  public async delete(id: string) {
    const model = new PersonNaturalDatabase()

    const existingItem = await model.findById(id)

    if (!existingItem) {
      throw new ReferenceError(Language.translate('person:notFound', { id }))
    }

    const personApp = new Person()

    await personApp.delete(existingItem.personId)

    return await model.delete(id)
  }
}
