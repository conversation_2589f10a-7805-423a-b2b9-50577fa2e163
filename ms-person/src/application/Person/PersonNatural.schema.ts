import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const PersonNaturalBaseSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
})

export const CreatePersonNaturalSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
})

export const UpdatePersonNaturalSchema = PersonNaturalBaseSchema.extend({
  id: z.uuid(),
  personId: z.uuid(),
}).partial({
  firstName: true,
  lastName: true,
})

export const FilterPersonNaturalSchema = createFilterSchema({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
})
