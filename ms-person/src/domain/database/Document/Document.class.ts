import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import type {
  DocumentCreateProps,
  DocumentModel,
  DocumentUpdateProps,
} from '@app/domain/database/Document'
import pt from '@app/domain/language/pt'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
  packages: { pt },
})

export class Document {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async create(params: DocumentCreateProps): Promise<DocumentModel> {
    const document = await this.prisma.document.create({ data: params })

    if (!document) {
      throw new ReferenceError(i18next.t('database:invalidData'))
    }

    return document
  }

  public async findAll(): Promise<DocumentModel[]> {
    const document = await this.prisma.document.findMany()

    if (!document) {
      throw new ReferenceError(i18next.t('person:notFoundAll'))
    }

    return document
  }

  public async findById(id: string): Promise<DocumentModel> {
    const document = await this.prisma.document.findFirst({
      where: { id, deleted: false },
    })

    if (!document) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return document
  }

  public async findByPersonId(personId: string): Promise<DocumentModel> {
    const document = await this.prisma.document.findFirst({
      where: { personId, deleted: false },
    })

    if (!document) {
      throw new ReferenceError(i18next.t('person:notFound', { id: personId }))
    }

    return document
  }

  public async update(params: DocumentUpdateProps) {
    const { id } = params

    const currentDocument = await this.prisma.document.findUnique({
      where: { id },
    })

    if (!currentDocument) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return this.prisma.document.update({
      where: { id },
      data: params,
    })
  }

  public async delete(id: string) {
    await this.prisma.document.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
