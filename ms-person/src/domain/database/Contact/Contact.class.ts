import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import type {
  ContactCreateProps,
  ContactModel,
  ContactUpdateProps,
} from '@app/domain/database/Contact'
import pt from '@app/domain/language/pt'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
  packages: { pt },
})

export class Contact {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async create(params: ContactCreateProps): Promise<ContactModel> {
    const contact = await this.prisma.contact.create({ data: params })

    if (!contact) {
      throw new ReferenceError(i18next.t('database:invalidData'))
    }

    return contact
  }

  public async findAll(): Promise<ContactModel[]> {
    const contact = await this.prisma.contact.findMany()

    if (!contact) {
      throw new ReferenceError(i18next.t('person:notFoundAll'))
    }

    return contact
  }

  public async findById(id: string): Promise<ContactModel> {
    const contact = await this.prisma.contact.findFirst({
      where: { id, deleted: false },
    })

    if (!contact) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return contact
  }

  public async findByPersonId(personId: string): Promise<ContactModel> {
    const contact = await this.prisma.contact.findFirst({
      where: { personId, deleted: false },
    })

    if (!contact) {
      throw new ReferenceError(i18next.t('person:notFound', { id: personId }))
    }

    return contact
  }

  public async update(params: ContactUpdateProps) {
    const { id } = params

    const currentAddress = await this.prisma.contact.findUnique({
      where: { id },
    })

    if (!currentAddress) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return this.prisma.contact.update({
      where: { id },
      data: params,
    })
  }

  public async delete(id: string) {
    await this.prisma.contact.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
