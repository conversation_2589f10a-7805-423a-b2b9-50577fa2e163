import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import type {
  PersonLegalCreateProps,
  PersonLegalModel,
  PersonLegalUpdateProps,
} from '@app/domain/database/PersonLegal'
import pt from '@app/domain/language/pt'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
  packages: { pt },
})

export class PersonLegal {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async create(
    params: PersonLegalCreateProps,
  ): Promise<PersonLegalModel> {
    const personLegal = await this.prisma.personLegal.create({ data: params })

    if (!personLegal) {
      throw new ReferenceError(i18next.t('database:invalidData'))
    }

    return personLegal
  }

  public async findAll(): Promise<PersonLegalModel[]> {
    const personLegal = await this.prisma.personLegal.findMany()

    if (!personLegal) {
      throw new ReferenceError(i18next.t('person:notFoundAll'))
    }

    return personLegal
  }

  public async findById(id: string) {
    const personLegal = await this.prisma.personLegal.findFirst({
      where: { id, deleted: false },
    })

    if (!personLegal) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return personLegal
  }

  public async findByPersonId(personId: string): Promise<PersonLegalModel> {
    const personLegal = await this.prisma.personLegal.findFirst({
      where: { personId, deleted: false },
    })

    if (!personLegal) {
      throw new ReferenceError(i18next.t('person:notFound', { id: personId }))
    }

    return personLegal
  }

  public async update(params: PersonLegalUpdateProps) {
    const { id } = params

    const currentPersonLegal = await this.prisma.personLegal.findUnique({
      where: { id },
    })

    if (!currentPersonLegal) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return this.prisma.personLegal.update({
      where: { id },
      data: params,
    })
  }

  public async delete(id: string) {
    await this.prisma.personLegal.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
