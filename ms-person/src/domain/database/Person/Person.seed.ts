import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  await prisma.person.upsert({
    where: { id: '550e8400-e29b-41d4-a716-446655440000' },
    update: {},
    create: {
      id: '550e8400-e29b-41d4-a716-446655440000',
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
