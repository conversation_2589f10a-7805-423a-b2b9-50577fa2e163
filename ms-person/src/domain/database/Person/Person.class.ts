import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import type {
  PersonCreateProps,
  PersonModel,
  PersonUpdateProps,
} from '@app/domain/database/Person'
import pt from '@app/domain/language/pt'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
  packages: { pt },
})

export class Person {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async create(params: PersonCreateProps = {}): Promise<PersonModel> {
    const person = await this.prisma.person.create({ data: params })

    if (!person) {
      throw new ReferenceError(i18next.t('database:invalidData'))
    }

    return person
  }

  public async findAll(): Promise<PersonModel[]> {
    const person = await this.prisma.person.findMany()

    if (!person) {
      throw new ReferenceError(i18next.t('person:notFoundAll'))
    }

    return person
  }

  public async findById(personId: string) {
    const person = await this.prisma.person.findFirst({
      where: { id: personId, deleted: false },
    })

    if (!person) {
      throw new ReferenceError(i18next.t('person:notFound', { id: personId }))
    }

    return person
  }

  public async update(params: PersonUpdateProps) {
    const { id } = params

    const currentPerson = await this.prisma.person.findUnique({
      where: { id },
    })

    if (!currentPerson) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return this.prisma.person.update({
      where: { id },
      data: params,
    })
  }

  public async delete(id: string) {
    await this.prisma.person.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
