import { PrismaClient } from '@prisma/client'
import { Decimal } from '@prisma/client/runtime/library'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateStoreCreditDto,
  FetchStoreCreditsProps,
  StoreCreditModel,
  UpdateStoreCreditDto,
  UseStoreCreditDto,
} from '@app/domain/database/StoreCredit/StoreCredit.d'
import pt from '@app/domain/language/pt'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
  packages: { pt },
})

export class StoreCredit {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<StoreCreditModel> {
    const storeCredit = await this.prisma.storeCredit.findFirst({
      where: { id, deleted: false },
    })

    if (!storeCredit) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'store credit' }),
      )
    }

    return storeCredit
  }

  public async findByPersonId(
    personId: string,
    filters?: { issuingStoreLegalPersonId?: string },
  ): Promise<StoreCreditModel[]> {
    const storeCredits = await this.prisma.storeCredit.findMany({
      where: {
        personId,
        deleted: false,
        ...(filters?.issuingStoreLegalPersonId && {
          issuingStoreLegalPersonId: filters.issuingStoreLegalPersonId,
        }),
      },
      orderBy: { createdAt: 'desc' },
    })

    return storeCredits
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    personId,
    issuingStoreLegalPersonId,
    currency,
  }: FetchStoreCreditsProps) {
    const skip = (+page - 1) * +perPage
    const take = +perPage

    const where = {
      deleted: false,
      ...(personId && { personId }),
      ...(issuingStoreLegalPersonId && { issuingStoreLegalPersonId }),
      ...(currency && { currency }),
      ...(search && {
        OR: [
          { notes: { contains: search } },
          { originatingProposalId: { contains: search } },
        ],
      }),
    }

    const [storeCredits, totalItems] = await Promise.all([
      this.prisma.storeCredit.findMany({
        where,
        skip,
        take,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.storeCredit.count({ where }),
    ])

    return {
      data: storeCredits,
      pagination: {
        page: +page,
        perPage: +perPage,
        totalItems,
        totalPages: Math.ceil(totalItems / +perPage),
      },
    }
  }

  public async create({
    personId,
    balance,
    currency = 'BRL',
    issuingStoreLegalPersonId,
    originatingProposalId,
    notes,
  }: CreateStoreCreditDto): Promise<StoreCreditModel> {
    // Verify person exists
    const person = await this.prisma.person.findFirst({
      where: { id: personId, deleted: false },
    })

    if (!person) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'Person' }),
      )
    }

    const storeCredit = await this.prisma.storeCredit.create({
      data: {
        personId,
        balance,
        currency,
        issuingStoreLegalPersonId,
        originatingProposalId,
        notes,
      },
    })

    return storeCredit
  }

  public async update({
    id,
    balance,
    currency,
    issuingStoreLegalPersonId,
    notes,
  }: UpdateStoreCreditDto): Promise<StoreCreditModel> {
    const storeCredit = await this.prisma.storeCredit.update({
      where: { id },
      data: {
        ...(balance !== undefined && { balance }),
        ...(currency && { currency }),
        ...(issuingStoreLegalPersonId && { issuingStoreLegalPersonId }),
        ...(notes !== undefined && { notes }),
      },
    })

    return storeCredit
  }

  public async useCredit({
    id,
    amountToUse,
    storeLegalPersonId,
  }: UseStoreCreditDto): Promise<StoreCreditModel> {
    const storeCredit = await this.findById(id)

    // Validate that the credit can be used at this store
    if (storeCredit.issuingStoreLegalPersonId !== storeLegalPersonId) {
      throw new ConflictException(
        i18next.t('storeCredit:invalidStore', {
          creditStore: storeCredit.issuingStoreLegalPersonId,
          requestedStore: storeLegalPersonId,
        }),
      )
    }

    // Check if there's sufficient balance
    if (storeCredit.balance.lt(amountToUse)) {
      throw new ConflictException(
        i18next.t('storeCredit:insufficientBalance', {
          available: storeCredit.balance.toString(),
          requested: amountToUse.toString(),
        }),
      )
    }

    // Deduct the amount
    const newBalance = storeCredit.balance.sub(amountToUse)

    const updatedStoreCredit = await this.prisma.storeCredit.update({
      where: { id },
      data: { balance: newBalance },
    })

    return updatedStoreCredit
  }

  public async getTotalBalance(
    personId: string,
    issuingStoreLegalPersonId?: string,
  ): Promise<Decimal> {
    const result = await this.prisma.storeCredit.aggregate({
      where: {
        personId,
        deleted: false,
        ...(issuingStoreLegalPersonId && { issuingStoreLegalPersonId }),
      },
      _sum: { balance: true },
    })

    return result._sum.balance || new Decimal(0)
  }

  public async delete(id: string): Promise<void> {
    await this.findById(id)

    await this.prisma.storeCredit.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
