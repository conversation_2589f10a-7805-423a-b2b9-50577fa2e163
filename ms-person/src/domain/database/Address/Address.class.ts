import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import type {
  AddressCreateProps,
  AddressModel,
  AddressUpdateProps,
} from '@app/domain/database/Address'
import pt from '@app/domain/language/pt'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
  packages: { pt },
})

export class Address {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async create(params: AddressCreateProps): Promise<AddressModel> {
    const address = await this.prisma.address.create({ data: params })

    if (!address) {
      throw new ReferenceError(i18next.t('database:invalidData'))
    }

    return address
  }

  public async findAll(): Promise<AddressModel[]> {
    const address = await this.prisma.address.findMany()

    if (!address) {
      throw new ReferenceError(i18next.t('person:notFoundAll'))
    }

    return address
  }

  public async findById(id: string): Promise<AddressModel> {
    const address = await this.prisma.address.findFirst({
      where: { id, deleted: false },
    })

    if (!address) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return address
  }

  public async findByPersonId(personId: string): Promise<AddressModel> {
    const address = await this.prisma.address.findFirst({
      where: { personId, deleted: false },
    })

    if (!address) {
      throw new ReferenceError(i18next.t('person:notFound', { id: personId }))
    }

    return address
  }

  public async update(params: AddressUpdateProps): Promise<AddressModel> {
    const { id } = params

    const currentAddress = await this.prisma.address.findUnique({
      where: { id },
    })

    if (!currentAddress) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return this.prisma.address.update({
      where: { id },
      data: params,
    })
  }

  public async delete(id: string) {
    await this.prisma.address.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
