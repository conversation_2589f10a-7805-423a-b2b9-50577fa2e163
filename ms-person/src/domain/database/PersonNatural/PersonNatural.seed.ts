import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function main() {
  await prisma.personNatural.upsert({
    where: { id: '123e4567-e89b-12d3-a456-426614174000' },
    update: {},
    create: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      firstName: 'Thrift',
      lastName: 'Natural',
      personId: '550e8400-e29b-41d4-a716-446655440000',
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
