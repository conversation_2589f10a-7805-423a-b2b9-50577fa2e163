export type PersonNaturalModel = {
  id: string
  personId: string
  firstName?: string | null
  lastName?: string | null
  createdAt: Date
  updatedAt: Date
  deleted: boolean
}

export type PersonNaturalCreateProps = Omit<
  PersonNaturalModel,
  'id' | 'createdAt' | 'updatedAt' | 'deleted'
>

export type PersonNaturalUpdateProps = Omit<
  PersonNaturalModel,
  'createdAt' | 'updatedAt' | 'deleted'
>
