import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import type {
  PersonNaturalCreateProps,
  PersonNaturalModel,
  PersonNaturalUpdateProps,
} from '@app/domain/database/PersonNatural'
import pt from '@app/domain/language/pt'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
  packages: { pt },
})

export class PersonNatural {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async create(
    params: PersonNaturalCreateProps,
  ): Promise<PersonNaturalModel> {
    const personNatural = await this.prisma.personNatural.create({
      data: params,
    })

    if (!personNatural) {
      throw new ReferenceError(i18next.t('database:invalidData'))
    }

    return personNatural
  }

  public async findAll(): Promise<PersonNaturalModel[]> {
    const personNatural = await this.prisma.personNatural.findMany()

    if (!personNatural) {
      throw new ReferenceError(i18next.t('person:notFoundAll'))
    }

    return personNatural
  }

  public async findById(id: string) {
    const personNatural = await this.prisma.personNatural.findFirst({
      where: { id, deleted: false },
    })

    if (!personNatural) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return personNatural
  }

  public async findByPersonId(personId: string): Promise<PersonNaturalModel> {
    const personNatural = await this.prisma.personNatural.findFirst({
      where: { personId, deleted: false },
    })

    if (!personNatural) {
      throw new ReferenceError(i18next.t('person:notFound', { id: personId }))
    }

    return personNatural
  }

  public async update(params: PersonNaturalUpdateProps) {
    const { id } = params

    const currentPersonNatural = await this.prisma.personNatural.findUnique({
      where: { id },
    })

    if (!currentPersonNatural) {
      throw new ReferenceError(i18next.t('person:notFound', { id }))
    }

    return this.prisma.personNatural.update({
      where: { id },
      data: params,
    })
  }

  public async delete(id: string) {
    await this.prisma.personNatural.update({
      where: { id },
      data: { deleted: true },
    })
  }
}
