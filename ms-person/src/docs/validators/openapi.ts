import SwaggerParser from '@apidevtools/swagger-parser'
import { readFileSync, existsSync } from 'fs'
import { resolve } from 'path'

async function validateOpenApiSpec() {
  const openApiPath = resolve(__dirname, '../../../docs/openapi.json')
  
  if (!existsSync(openApiPath)) {
    console.error('❌ OpenAPI specification not found. Please run "yarn docs:generate" first.')
    process.exit(1)
  }

  console.log('🔍 Validating OpenAPI specification...')
  
  try {
    // Parse and validate with swagger-parser
    console.log('📋 Parsing OpenAPI specification...')
    const api = await SwaggerParser.validate(openApiPath)
    console.log('✅ OpenAPI specification is valid!')
    console.log(`   • Title: ${api.info.title}`)
    console.log(`   • Version: ${api.info.version}`)
    console.log(`   • Paths: ${Object.keys(api.paths || {}).length}`)
    console.log(`   • Components: ${Object.keys(api.components?.schemas || {}).length} schemas`)

    // Basic validation checks
    console.log('\n🔍 Performing basic validation checks...')

    const openApiContent = JSON.parse(readFileSync(openApiPath, 'utf-8'))

    // Check required fields
    const requiredFields = ['openapi', 'info', 'paths']
    const missingFields = requiredFields.filter(field => !openApiContent[field])

    if (missingFields.length > 0) {
      console.log(`❌ Missing required fields: ${missingFields.join(', ')}`)
      process.exit(1)
    }

    // Check info object
    if (!openApiContent.info.title || !openApiContent.info.version) {
      console.log('❌ Missing required info fields (title, version)')
      process.exit(1)
    }

    // Check paths
    const pathCount = Object.keys(openApiContent.paths).length
    if (pathCount === 0) {
      console.log('⚠️  No paths defined in the specification')
    }

    // Check components
    const componentCount = openApiContent.components ? Object.keys(openApiContent.components.schemas || {}).length : 0

    console.log('✅ Basic validation checks passed!')
    console.log(`   • Paths: ${pathCount}`)
    console.log(`   • Components: ${componentCount}`)
    
    console.log('\n✅ OpenAPI specification validation completed successfully!')
    
  } catch (error) {
    console.error('❌ OpenAPI specification validation failed:')
    if (error instanceof Error) {
      console.error(`   • ${error.message}`)
      
      // Show more details for parsing errors
      if ('details' in error && Array.isArray(error.details)) {
        error.details.forEach((detail: any) => {
          console.error(`   • ${detail.message} at ${detail.path}`)
        })
      }
    } else {
      console.error('   • Unknown error occurred')
    }
    process.exit(1)
  }
}

// Run validation if called directly
if (require.main === module) {
  validateOpenApiSpec()
}

export { validateOpenApiSpec }
