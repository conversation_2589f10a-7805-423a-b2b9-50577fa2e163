import express from 'express'
import swaggerUi from 'swagger-ui-express'
import { readFileSync, existsSync } from 'fs'
import { resolve } from 'path'

const app = express()
const PORT = process.env.DOCS_PORT || 3001

// Load OpenAPI specification
const openApiPath = resolve(__dirname, '../../docs/openapi.json')

if (!existsSync(openApiPath)) {
  console.error('❌ OpenAPI specification not found. Please run "yarn docs:generate" first.')
  process.exit(1)
}

const openApiSpec = JSON.parse(readFileSync(openApiPath, 'utf-8'))

// Swagger UI options
const swaggerOptions = {
  explorer: true,
  swaggerOptions: {
    docExpansion: 'list',
    filter: true,
    showRequestDuration: true,
    tryItOutEnabled: true,
    requestInterceptor: (req: any) => {
      // Add any request interceptors here
      return req
    }
  },
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #3b82f6 }
    .swagger-ui .scheme-container { background: #f8fafc; padding: 10px; border-radius: 4px; }
  `,
  customSiteTitle: 'Person Microservice API Documentation'
}

// Serve Swagger UI
app.use('/docs', swaggerUi.serve, swaggerUi.setup(openApiSpec, swaggerOptions))

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'person-docs',
    timestamp: new Date().toISOString(),
    version: openApiSpec.info.version
  })
})

// Redirect root to docs
app.get('/', (req, res) => {
  res.redirect('/docs')
})

// Serve raw OpenAPI spec
app.get('/openapi.json', (req, res) => {
  res.json(openApiSpec)
})

// Start server
app.listen(PORT, () => {
  console.log(`📚 Documentation server running at:`)
  console.log(`   • Swagger UI: http://localhost:${PORT}/docs`)
  console.log(`   • OpenAPI JSON: http://localhost:${PORT}/openapi.json`)
  console.log(`   • Health Check: http://localhost:${PORT}/health`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('📚 Documentation server shutting down...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('📚 Documentation server shutting down...')
  process.exit(0)
})
