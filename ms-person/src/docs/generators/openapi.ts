import { writeFileSync, readFileSync } from 'fs'
import { resolve } from 'path'
import { z } from '../zod-openapi-init'
import { OpenAPIRegistry, OpenApiGeneratorV3 } from '@asteasolutions/zod-to-openapi'

// Import schemas
import { PersonNaturalBaseSchema, CreatePersonNaturalSchema, UpdatePersonNaturalSchema, FilterPersonNaturalSchema } from '@app/application/Person/PersonNatural.schema'
import { StoreCreditBaseSchema, CreateStoreCreditSchema, UpdateStoreCreditSchema } from '@app/application/StoreCredit/StoreCredit.schema'
import { registerRoutes } from './routes'

// Load shared schemas
const commonSchemas = JSON.parse(readFileSync(resolve(__dirname, '../../../docs/schemas/common.json'), 'utf-8'))
const errorSchemas = JSON.parse(readFileSync(resolve(__dirname, '../../../docs/schemas/errors.json'), 'utf-8'))
const requestSchemas = JSON.parse(readFileSync(resolve(__dirname, '../../../docs/schemas/requests.json'), 'utf-8'))
const responseSchemas = JSON.parse(readFileSync(resolve(__dirname, '../../../docs/schemas/responses.json'), 'utf-8'))

// Create OpenAPI registry
const registry = new OpenAPIRegistry()

// Register common schemas
const UUIDSchema = z.string().uuid().openapi({
  description: 'UUID identifier',
  example: '123e4567-e89b-12d3-a456-************'
})

const TimestampSchema = z.string().datetime().openapi({
  description: 'ISO 8601 timestamp',
  example: '2024-01-15T10:30:00.000Z'
})

registry.register('UUID', UUIDSchema)
registry.register('Timestamp', TimestampSchema)

// Register Person schemas
registry.register('PersonNaturalBase', PersonNaturalBaseSchema.openapi({
  description: 'Base schema for natural person'
}))

registry.register('CreatePersonNatural', CreatePersonNaturalSchema.openapi({
  description: 'Schema for creating a natural person'
}))

registry.register('UpdatePersonNatural', UpdatePersonNaturalSchema.openapi({
  description: 'Schema for updating a natural person'
}))

registry.register('FilterPersonNatural', FilterPersonNaturalSchema.openapi({
  description: 'Schema for filtering natural persons'
}))

// Register StoreCredit schemas
registry.register('StoreCreditBase', StoreCreditBaseSchema.openapi({
  description: 'Base schema for store credit'
}))

registry.register('CreateStoreCredit', CreateStoreCreditSchema.openapi({
  description: 'Schema for creating store credit'
}))

registry.register('UpdateStoreCredit', UpdateStoreCreditSchema.openapi({
  description: 'Schema for updating store credit'
}))

// Define response schemas using registered UUID and Timestamp schemas
const PersonResponse = z.object({
  id: UUIDSchema,
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema
}).openapi({
  description: 'Person response object'
})

const PersonNaturalResponse = z.object({
  id: UUIDSchema,
  personId: UUIDSchema,
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema
}).openapi({
  description: 'Natural person response object'
})

const PersonLegalResponse = z.object({
  id: UUIDSchema,
  personId: UUIDSchema,
  fictitiousName: z.string(),
  legalName: z.string(),
  nickname: z.string(),
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema
}).openapi({
  description: 'Legal person response object'
})

const AddressResponse = z.object({
  id: UUIDSchema,
  personId: UUIDSchema,
  line1: z.string(),
  number: z.string(),
  line2: z.string(),
  city: z.string(),
  region: z.string(),
  country: z.string(),
  postalCode: z.string(),
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema
}).openapi({
  description: 'Address response object'
})

const ContactResponse = z.object({
  id: UUIDSchema,
  personId: UUIDSchema,
  label: z.string(),
  type: z.string(),
  value: z.string(),
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema
}).openapi({
  description: 'Contact response object'
})

const StoreCreditResponse = z.object({
  id: UUIDSchema,
  personId: UUIDSchema,
  balance: z.number(),
  currency: z.string(),
  issuingStoreLegalPersonId: UUIDSchema,
  originatingProposalId: UUIDSchema,
  notes: z.string().optional(),
  createdAt: TimestampSchema,
  updatedAt: TimestampSchema
}).openapi({
  description: 'Store credit response object'
})

// Register response schemas
registry.register('PersonResponse', PersonResponse)
registry.register('PersonNaturalResponse', PersonNaturalResponse)
registry.register('PersonLegalResponse', PersonLegalResponse)
registry.register('AddressResponse', AddressResponse)
registry.register('ContactResponse', ContactResponse)
registry.register('StoreCreditResponse', StoreCreditResponse)

// Standard API response wrapper
const ApiResponse = <T extends z.ZodTypeAny>(dataSchema: T) => z.object({
  code: z.string(),
  message: z.string(),
  data: dataSchema.optional()
})

registry.register('ApiResponsePerson', ApiResponse(PersonResponse))
registry.register('ApiResponsePersonNatural', ApiResponse(PersonNaturalResponse))
registry.register('ApiResponsePersonLegal', ApiResponse(PersonLegalResponse))
registry.register('ApiResponseAddress', ApiResponse(AddressResponse))
registry.register('ApiResponseContact', ApiResponse(ContactResponse))
registry.register('ApiResponseStoreCredit', ApiResponse(StoreCreditResponse))

// Array responses
registry.register('ApiResponsePersonArray', ApiResponse(z.array(PersonResponse)))
registry.register('ApiResponsePersonNaturalArray', ApiResponse(z.array(PersonNaturalResponse)))
registry.register('ApiResponsePersonLegalArray', ApiResponse(z.array(PersonLegalResponse)))
registry.register('ApiResponseAddressArray', ApiResponse(z.array(AddressResponse)))
registry.register('ApiResponseContactArray', ApiResponse(z.array(ContactResponse)))
registry.register('ApiResponseStoreCreditArray', ApiResponse(z.array(StoreCreditResponse)))

export { registry }

// Generate OpenAPI spec if run directly
if (require.main === module) {
  console.log('Generating OpenAPI specification...')

  // Register all routes
  registerRoutes(registry)

  const generator = new OpenApiGeneratorV3(registry.definitions)

  const docs = generator.generateDocument({
    openapi: '3.0.0',
    info: {
      version: '1.0.0',
      title: 'Person Microservice API',
      description: 'API for managing person data, addresses, contacts, and store credits',
      contact: {
        name: 'Thrift Technology',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: 'http://localhost:8080/person',
        description: 'Development server'
      }
    ],
    tags: [
      { name: 'Person', description: 'Person management operations' },
      { name: 'PersonNatural', description: 'Natural person operations' },
      { name: 'PersonLegal', description: 'Legal person operations' },
      { name: 'Address', description: 'Address management operations' },
      { name: 'Contact', description: 'Contact management operations' },
      { name: 'StoreCredit', description: 'Store credit operations' }
    ]
  })

  // Merge with all shared schemas
  docs.components = {
    ...docs.components,
    schemas: {
      ...docs.components?.schemas,
      ...commonSchemas.components?.schemas,
      ...errorSchemas.components?.schemas,
      ...requestSchemas.components?.schemas,
      ...responseSchemas.components?.schemas
    }
  }

  // Write the OpenAPI specification
  const outputPath = resolve(__dirname, '../../../docs/openapi.json')
  writeFileSync(outputPath, JSON.stringify(docs, null, 2))

  console.log(`✅ OpenAPI specification generated at: ${outputPath}`)
}
