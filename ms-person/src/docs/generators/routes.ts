import { OpenAPIRegistry } from '@asteasolutions/zod-to-openapi'
import { z } from '../zod-openapi-init'

export function registerRoutes(registry: OpenAPIRegistry) {
  // Person routes
  registry.registerPath({
    method: 'get',
    path: '/person',
    tags: ['Person'],
    summary: 'Get all persons',
    description: 'Retrieve a list of all persons',
    responses: {
      200: {
        description: 'List of persons retrieved successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponsePersonArray'
            }
          }
        }
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  registry.registerPath({
    method: 'get',
    path: '/person/{personId}',
    tags: ['Person'],
    summary: 'Get person by ID',
    description: 'Retrieve a specific person by their ID',
    request: {
      params: z.object({
        personId: z.string().uuid().openapi({
          description: 'Person ID',
          example: '123e4567-e89b-12d3-a456-************'
        })
      })
    },
    responses: {
      200: {
        description: 'Person retrieved successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponsePerson'
            }
          }
        }
      },
      404: {
        $ref: '#/components/responses/NotFound'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  // PersonNatural routes
  registry.registerPath({
    method: 'post',
    path: '/person-natural',
    tags: ['PersonNatural'],
    summary: 'Create natural person',
    description: 'Create a new natural person',
    request: {
      body: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/CreatePersonNatural'
            }
          }
        }
      }
    },
    responses: {
      201: {
        description: 'Natural person created successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponsePersonNatural'
            }
          }
        }
      },
      400: {
        $ref: '#/components/responses/BadRequest'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  registry.registerPath({
    method: 'get',
    path: '/person-natural',
    tags: ['PersonNatural'],
    summary: 'Get all natural persons',
    description: 'Retrieve a list of all natural persons with optional filtering',
    request: {
      query: z.object({
        page: z.number().int().min(1).optional(),
        perPage: z.number().int().min(1).max(100).optional(),
        search: z.string().optional(),
        firstName: z.string().optional(),
        lastName: z.string().optional()
      }).openapi({
        description: 'Query parameters for filtering and pagination'
      })
    },
    responses: {
      200: {
        description: 'List of natural persons retrieved successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponsePersonNaturalArray'
            }
          }
        }
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  registry.registerPath({
    method: 'get',
    path: '/person-natural/{id}',
    tags: ['PersonNatural'],
    summary: 'Get natural person by ID',
    description: 'Retrieve a specific natural person by their ID',
    request: {
      params: z.object({
        id: z.string().uuid().openapi({
          description: 'Natural person ID',
          example: '123e4567-e89b-12d3-a456-************'
        })
      })
    },
    responses: {
      200: {
        description: 'Natural person retrieved successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponsePersonNatural'
            }
          }
        }
      },
      404: {
        $ref: '#/components/responses/NotFound'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  registry.registerPath({
    method: 'put',
    path: '/person-natural/{id}',
    tags: ['PersonNatural'],
    summary: 'Update natural person',
    description: 'Update an existing natural person',
    request: {
      params: z.object({
        id: z.string().uuid().openapi({
          description: 'Natural person ID',
          example: '123e4567-e89b-12d3-a456-************'
        })
      }),
      body: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/UpdatePersonNatural'
            }
          }
        }
      }
    },
    responses: {
      200: {
        description: 'Natural person updated successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponsePersonNatural'
            }
          }
        }
      },
      400: {
        $ref: '#/components/responses/BadRequest'
      },
      404: {
        $ref: '#/components/responses/NotFound'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  registry.registerPath({
    method: 'delete',
    path: '/person-natural/{id}',
    tags: ['PersonNatural'],
    summary: 'Delete natural person',
    description: 'Delete an existing natural person',
    request: {
      params: z.object({
        id: z.string().uuid().openapi({
          description: 'Natural person ID',
          example: '123e4567-e89b-12d3-a456-************'
        })
      })
    },
    responses: {
      204: {
        description: 'Natural person deleted successfully'
      },
      404: {
        $ref: '#/components/responses/NotFound'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  // PersonLegal routes
  registry.registerPath({
    method: 'post',
    path: '/person-legal',
    tags: ['PersonLegal'],
    summary: 'Create legal person',
    description: 'Create a new legal person',
    request: {
      body: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                fictitiousName: { type: 'string' },
                legalName: { type: 'string' },
                nickname: { type: 'string' }
              },
              required: ['fictitiousName', 'legalName', 'nickname']
            }
          }
        }
      }
    },
    responses: {
      201: {
        description: 'Legal person created successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponsePersonLegal'
            }
          }
        }
      },
      400: {
        $ref: '#/components/responses/BadRequest'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  // Address routes
  registry.registerPath({
    method: 'get',
    path: '/address/{id}',
    tags: ['Address'],
    summary: 'Get address by ID',
    description: 'Retrieve a specific address by its ID',
    request: {
      params: z.object({
        id: z.string().uuid().openapi({
          description: 'Address ID',
          example: '123e4567-e89b-12d3-a456-************'
        })
      })
    },
    responses: {
      200: {
        description: 'Address retrieved successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponseAddress'
            }
          }
        }
      },
      404: {
        $ref: '#/components/responses/NotFound'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  // Contact routes
  registry.registerPath({
    method: 'get',
    path: '/contact/{id}',
    tags: ['Contact'],
    summary: 'Get contact by ID',
    description: 'Retrieve a specific contact by its ID',
    request: {
      params: z.object({
        id: z.string().uuid().openapi({
          description: 'Contact ID',
          example: '123e4567-e89b-12d3-a456-************'
        })
      })
    },
    responses: {
      200: {
        description: 'Contact retrieved successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponseContact'
            }
          }
        }
      },
      404: {
        $ref: '#/components/responses/NotFound'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })

  // StoreCredit routes
  registry.registerPath({
    method: 'post',
    path: '/store-credits',
    tags: ['StoreCredit'],
    summary: 'Create store credit',
    description: 'Create a new store credit record',
    request: {
      body: {
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/CreateStoreCredit'
            }
          }
        }
      }
    },
    responses: {
      201: {
        description: 'Store credit created successfully',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ApiResponseStoreCredit'
            }
          }
        }
      },
      400: {
        $ref: '#/components/responses/BadRequest'
      },
      500: {
        $ref: '#/components/responses/InternalServerError'
      }
    }
  })
}
