{"name": "@thrift/person", "private": true, "version": "0.1.0", "description": "Person application service", "author": "Thrift Technology <<EMAIL>>", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/thrift-technology/ms-person.git"}, "scripts": {"build": "yarn healthcheck && tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "dev": "yarn healthcheck && tsx watch src/main.ts", "docs:generate": "tsx src/docs/generators/openapi.ts", "docs:serve": "tsx src/docs/serve.ts", "docs:validate": "tsx src/docs/validators/openapi.ts", "format": "prettier --check .", "format:fix": "prettier --write .", "healthcheck": "yarn lint && yarn format && yarn docs:generate && yarn docs:validate && tsc --project tsconfig.json --noEmit", "lint": "eslint ./src --ext .ts", "lint:fix": "yarn lint --fix", "mocks:start": "tsx src/mocks/server.ts", "mocks:generate": "tsx src/mocks/generators/fixtures.ts", "postinstall": "prisma generate", "prepare": "husky", "start": "node dist/src/main.js", "typecheck": "tsc --noEmit", "prisma:studio": "prisma studio"}, "devDependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@asteasolutions/zod-to-openapi": "^8.0.0", "@tsconfig/node24": "^24.0.1", "@types/node": "24.1.0", "@types/swagger-ui-express": "^4.1.6", "eslint": "9.32.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-prettier": "5.5.3", "eslint-plugin-unused-imports": "4.1.4", "husky": "^9.1.7", "msw": "^2.6.4", "prettier": "3.6.2", "swagger-jsdoc": "^6.2.8", "tsc-alias": "1.8.16", "tsx": "4.20.3", "typescript": "5.8.3", "typescript-eslint": "8.38.0"}, "dependencies": {"@prisma/client": "6.13.0", "@thrift/common": "https://github.com/thrift-technology/ms-common.git#main", "@thrift/i18n": "https://github.com/thrift-technology/i18n.git#main", "prisma": "6.13.0", "swagger-ui-express": "^5.0.1"}, "engines": {"node": ">=20 <24", "npm": ">=10 <12", "yarn": ">=1.22 <2"}, "prisma": {"schema": "./prisma/schema"}, "packageManager": "yarn@4.9.2"}