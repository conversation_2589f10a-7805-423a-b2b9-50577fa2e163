import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useLoginSchema, type UseLoginSchemaType } from '@app/application/Login'
import { useLoginMutation } from '@app/domain/Login'
import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'
import { useAuthStore } from '@app/domain/stores/Auth'
import { useEffect } from 'react'

export const useLogin = () => {
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const LoginSchema = useLoginSchema()
  const token = useAuthStore((state) => state.token)
  const setUser = useAuthStore((state) => state.setUser)

  useEffect(() => {
    if (token) {
      window.location.href = '/backoffice'
    }
  }, [token])

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<UseLoginSchemaType>({
    resolver: zodResolver(LoginSchema),
  })

  const { postLoginAsync, loading } = useLoginMutation()

  const onSubmit = async (params: UseLoginSchemaType) => {
    try {
      const data = await postLoginAsync(params)

      setUser(data)
      window.location.href = '/backoffice'
    } catch {
      showToast({
        title: translate('login:loginFailedTitle'),
        message: translate('login:loginFailedMessage'),
        type: 'error',
      })
    }
  }

  return {
    handleSubmit: handleSubmit(onSubmit),
    register,
    errors,
    loading,
  }
}
