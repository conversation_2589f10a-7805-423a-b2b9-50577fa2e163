import { z } from 'zod'
import { useLanguage } from '@app/application/Language'
import { InputTextSchema } from '@thrift/design-system/packages/molecules/InputText'

export const useTenantSchema = () => {
  const { translate } = useLanguage()

  return z.object({
    accountName: InputTextSchema({
      max: { value: 50, message: translate('tenant:fieldMax', { max: 63 }) },
      min: { value: 5, message: translate('tenant:fieldMin', { min: 3 }) },
      required: { value: true, message: translate('tenant:fieldRequired') },
    }).regex(/^(?!-)[a-z0-9-]+(?<!-)$/, translate('tenant:fieldInvalid')),
  })
}

export type UseTenantSchemaType = z.infer<ReturnType<typeof useTenantSchema>>
