import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect } from 'react'
import {
  type UseTenantSchemaType,
  useTenantSchema,
} from '@app/application/Tenant'
import { useAuthStore } from '@app/domain/stores/Auth'
import { buildSubdomainUrl, getSubdomain } from '@app/utils/subdomain'

export const useTenant = () => {
  const TenantSchema = useTenantSchema()
  const token = useAuthStore((state) => state.token)
  const setTenant = useAuthStore((state) => state.setTenant)

  useEffect(() => {
    if (token) {
      window.location.href = '/backoffice'
    }
  }, [token])

  useEffect(() => {
    const subdomain = getSubdomain()

    if (subdomain) {
      setTenant(subdomain)
      window.location.href = buildSubdomainUrl(subdomain) + '/home/<USER>'
    }
  }, [setTenant])

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<UseTenantSchemaType>({
    resolver: zodResolver(TenantSchema),
  })

  const onSubmit = (params: UseTenantSchemaType) => {
    setTenant(params.accountName)

    const subdomainUrl = buildSubdomainUrl(params.accountName)

    window.location.href = `${subdomainUrl}/home/<USER>
  }

  return {
    handleSubmit: handleSubmit(onSubmit),
    register,
    errors,
  }
}
