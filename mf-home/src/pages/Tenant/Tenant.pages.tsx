import { LoginWrap } from '@thrift/design-system/packages/templates/Login'
import { SignIn } from '@thrift/design-system/packages/organisms/SignIn'
import { SignInBrandInfo } from '@thrift/design-system/packages/organisms/SignInBrandInfo'
import { But<PERSON> } from '@thrift/design-system/packages/molecules/Button'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { useTenant } from '@app/application/Tenant'
import { useLanguage } from '@app/application/Language'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const Tenant = () => {
  const { handleSubmit, register, errors } = useTenant()
  const { translate } = useLanguage()

  return (
    <LoginWrap>
      <SignInBrandInfo />
      <SignIn>
        <Typography>{translate('tenant:description')}</Typography>
        <InputText
          {...register('accountName')}
          label={translate('tenant:accountName')}
          errorMessage={errors.accountName?.message}
        />
        <Button type="button" onClick={handleSubmit}>
          {translate('tenant:button')}
        </Button>
      </SignIn>
    </LoginWrap>
  )
}
