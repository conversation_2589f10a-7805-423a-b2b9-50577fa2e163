/**
 * Gets the current domain from window.location
 * Returns the base domain (e.g., 'localhost' or 'thrift.technology')
 */
const getCurrentDomain = (): string => {
  const hostname = window.location.hostname

  // If it's localhost, return 'localhost'
  if (hostname === 'localhost' || hostname.endsWith('.localhost')) {
    return 'localhost'
  }

  // For production, extract the base domain
  // e.g., 'home.thrift.technology' -> 'thrift.technology'
  const parts = hostname.split('.')

  if (parts.length >= 2) {
    return parts.slice(-2).join('.')
  }

  return hostname
}

/**
 * Builds the subdomain URL for redirection
 */
const buildSubdomainUrl = (subdomain: string): string => {
  const currentDomain = getCurrentDomain()
  const protocol = window.location.protocol
  const port = window.location.port ? `:${window.location.port}` : ''

  return `${protocol}//${subdomain}.${currentDomain}${port}`
}

const getSubdomain = () => {
  const hostname = window.location.hostname
  const parts = hostname.split('.')

  if (hostname === 'localhost' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return null
  }

  if (parts.length > 2 || (parts.length === 2 && parts[0] !== 'www')) {
    return parts[0]
  }

  return null
}

export { buildSubdomainUrl, getSubdomain }
