import { create } from 'zustand'
import {
  createJSONStorage,
  persist,
  type StateStorage,
} from 'zustand/middleware'
import type { IAuthState } from '@app/domain/stores/Auth'
import type { LoginResponse } from '@app/domain/Login'

const AuthStorage: StateStorage = {
  setItem: (name, value) => {
    return localStorage.setItem(name, value)
  },
  getItem: (name) => {
    const value = localStorage.getItem(name)

    return value ?? null
  },
  removeItem: (name) => {
    return localStorage.removeItem(name)
  },
}

const initialState = {
  user: {} as LoginResponse,
  token: '',
  refreshToken: '',
  tenant: '',
}

export const useAuthStore = create<IAuthState>()(
  persist(
    (set) => ({
      ...initialState,
      setToken: (token) => set({ token }),
      setRefreshToken: (refreshToken) => set({ refreshToken }),
      setUser: (user) => set({ user }),
      setTenant: (tenant) => set({ tenant }),
      reset: () => set(initialState),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AuthStorage),
    },
  ),
)
