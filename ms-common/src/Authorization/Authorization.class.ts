/**
 * Authorization utility class for handling route pattern matching and authorization logic
 */
export class Authorization {
  /**
   * Check if a URL matches any of the public route patterns
   * Supports:
   * - Exact matches: "/tenants/123"
   * - Wildcard patterns: "/tenants/*"
   * - Express-style parameters: "/tenants/:id", "/tenants/:id/verify"
   * - Mixed patterns: "/tenants/:id/*"
   */
  public static isPublicRoute(url: string, publicPatterns: string[]): boolean {
    return publicPatterns.some((pattern) => {
      // Support exact matches
      if (pattern === url) return true

      // Support wildcard patterns like /tenants/*
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '[^/]+')
        const regex = new RegExp(`^${regexPattern}$`)

        return regex.test(url)
      }

      // Support Express-style parameters like /tenants/:id or /tenants/:id/verify
      if (pattern.includes(':')) {
        const regexPattern = pattern.replace(/:[^/]+/g, '[^/]+')
        const regex = new RegExp(`^${regexPattern}$`)

        return regex.test(url)
      }

      return false
    })
  }

  /**
   * Convert Express-style route pattern to regex pattern
   * Examples:
   * - "/tenants/:id" -> "/tenants/[^/]+"
   * - "/tenants/:id/verify" -> "/tenants/[^/]+/verify"
   * - "/tenants/:id/*" -> "/tenants/[^/]+/[^/]+"
   */
  public static patternToRegex(pattern: string): RegExp {
    const regexPattern = pattern
      .replace(/:[^/]+/g, '[^/]+') // Replace :param with [^/]+
      .replace(/\*/g, '[^/]+') // Replace * with [^/]+
      .replace(/\//g, '\\/') // Escape forward slashes

    return new RegExp(`^${regexPattern}$`)
  }

  /**
   * Extract parameter values from URL based on pattern
   * Example:
   * - pattern: "/tenants/:id/verify", url: "/tenants/123/verify" -> { id: "123" }
   */
  public static extractParams(
    pattern: string,
    url: string,
  ): Record<string, string> {
    const params: Record<string, string> = {}
    const patternParts = pattern.split('/')
    const urlParts = url.split('/')

    if (patternParts.length !== urlParts.length) {
      return params
    }

    for (let i = 0; i < patternParts.length; i++) {
      const patternPart = patternParts[i]
      const urlPart = urlParts[i]

      if (patternPart.startsWith(':')) {
        const paramName = patternPart.slice(1)

        params[paramName] = urlPart
      }
    }

    return params
  }
}
