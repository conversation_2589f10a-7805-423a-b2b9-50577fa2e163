/**
 * Utility class for extracting subdomains from URLs and hostnames
 */
export class SubdomainExtractor {
  /**
   * Extract the first subdomain from a hostname or URL
   * @param input - The hostname or URL to extract subdomain from
   * @returns The first subdomain or null if not found
   */
  public static extract(input: string): string | null {
    if (!input) {
      return null
    }

    // Remove protocol if present
    const cleanInput = input.replace(/^https?:\/\//, '')

    // Split by dots and get the first part (subdomain)
    const parts = cleanInput.split('.')

    // If only one part or empty, return null
    if (parts.length <= 1 || !parts[0]) {
      return null
    }

    return parts[0]
  }
}
