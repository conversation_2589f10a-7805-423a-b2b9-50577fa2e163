/**
 * Authorization utility class for handling route pattern matching and authorization logic
 */
export declare class Authorization {
    /**
     * Check if a URL matches any of the public route patterns
     * Supports:
     * - Exact matches: "/tenants/123"
     * - Wildcard patterns: "/tenants/*"
     * - Express-style parameters: "/tenants/:id", "/tenants/:id/verify"
     * - Mixed patterns: "/tenants/:id/*"
     */
    static isPublicRoute(url: string, publicPatterns: string[]): boolean;
    /**
     * Convert Express-style route pattern to regex pattern
     * Examples:
     * - "/tenants/:id" -> "/tenants/[^/]+"
     * - "/tenants/:id/verify" -> "/tenants/[^/]+/verify"
     * - "/tenants/:id/*" -> "/tenants/[^/]+/[^/]+"
     */
    static patternToRegex(pattern: string): RegExp;
    /**
     * Extract parameter values from URL based on pattern
     * Example:
     * - pattern: "/tenants/:id/verify", url: "/tenants/123/verify" -> { id: "123" }
     */
    static extractParams(pattern: string, url: string): Record<string, string>;
}
