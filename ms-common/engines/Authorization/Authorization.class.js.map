{"version": 3, "file": "Authorization.class.js", "sourceRoot": "", "sources": ["../../src/Authorization/Authorization.class.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,MAAa,aAAa;IACxB;;;;;;;OAOG;IACI,MAAM,CAAC,aAAa,CAAC,GAAW,EAAE,cAAwB;QAC/D,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACrC,wBAAwB;YACxB,IAAI,OAAO,KAAK,GAAG;gBAAE,OAAO,IAAI,CAAA;YAEhC,4CAA4C;YAC5C,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;gBACpD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,YAAY,GAAG,CAAC,CAAA;gBAE7C,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxB,CAAC;YAED,4EAA4E;YAC5E,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBACxD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,YAAY,GAAG,CAAC,CAAA;gBAE7C,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACxB,CAAC;YAED,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,cAAc,CAAC,OAAe;QAC1C,MAAM,YAAY,GAAG,OAAO;aACzB,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,4BAA4B;aACxD,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,uBAAuB;aAC/C,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA,CAAC,yBAAyB;QAElD,OAAO,IAAI,MAAM,CAAC,IAAI,YAAY,GAAG,CAAC,CAAA;IACxC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,aAAa,CACzB,OAAe,EACf,GAAW;QAEX,MAAM,MAAM,GAA2B,EAAE,CAAA;QACzC,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACvC,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC5C,OAAO,MAAM,CAAA;QACf,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;YACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YAE3B,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;gBAEtC,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,CAAA;YAC7B,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AAhFD,sCAgFC"}