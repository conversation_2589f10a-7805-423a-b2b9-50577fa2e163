"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubdomainExtractor = void 0;
/**
 * Utility class for extracting subdomains from URLs and hostnames
 */
class SubdomainExtractor {
    /**
     * Extract the first subdomain from a hostname or URL
     * @param input - The hostname or URL to extract subdomain from
     * @returns The first subdomain or null if not found
     */
    static extract(input) {
        if (!input) {
            return null;
        }
        // Remove protocol if present
        const cleanInput = input.replace(/^https?:\/\//, '');
        // Split by dots and get the first part (subdomain)
        const parts = cleanInput.split('.');
        // If only one part or empty, return null
        if (parts.length <= 1 || !parts[0]) {
            return null;
        }
        return parts[0];
    }
}
exports.SubdomainExtractor = SubdomainExtractor;
//# sourceMappingURL=SubdomainExtractor.js.map