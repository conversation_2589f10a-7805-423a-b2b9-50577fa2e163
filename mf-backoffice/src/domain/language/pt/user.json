{"name": "Nome", "lastName": "Sobrenome", "email": "E-mail", "role": "Função", "active": "Ativo", "inactive": "Inativo", "statusDescription": "<PERSON><PERSON><PERSON> acesso à plataforma", "access": "Ace<PERSON>", "personalInformation": "Informaçõ<PERSON>", "fieldMax": "O campo deve ter no máximo {{max}} caracteres", "fieldMin": "O campo deve ter no mínimo {{min}} caracteres", "fieldRequired": "O campo é obrigatório", "emptyTable": "Nenhum usuário encontrado", "addSubmit": "Usuário adicionado com sucesso", "editSubmit": "Usuário editado com sucesso", "errorSubmitting": "Erro ao enviar o formulário", "deleted": "Usuário excluído com sucesso", "errorDeleting": "Erro ao excluir o usuário", "emailInvalid": "Campo deve ser um endereço de e-mail válido"}