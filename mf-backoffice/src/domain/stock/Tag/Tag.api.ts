import { api, type ApiResponse } from '@app/domain/services'
import type { Tag, TagListParams } from '@app/domain/stock/Tag'

export const getTags = async (params: TagListParams) => {
  const response = await api.get<ApiResponse<Tag[]>>(
    `/stock/tag?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data?.data
}

export const getTagById = async (id: string) => {
  const response = await api.get<ApiResponse<Tag>>(`/stock/tag/${id}`)

  return response?.data?.data
}

export const postTag = async (params: Omit<Tag, 'id'>) => {
  const response = await api.post<ApiResponse<Tag>>('/stock/tag', params)

  return response?.data?.data
}

export const putTag = async (params: Tag) => {
  const { id, ...body } = params

  const response = await api.put<ApiResponse<Tag>>(`/stock/tag/${id}`, body)

  return response?.data?.data
}

export const deleteTag = async (id: string) => {
  const response = await api.delete<ApiResponse<void>>(`/stock/tag/${id}`)

  return response?.data?.data
}
