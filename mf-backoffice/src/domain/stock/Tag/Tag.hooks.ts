import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  postTag,
  putTag,
  getTags,
  getTagById,
  deleteTag,
  type TagListParams,
  type Tag,
} from '@app/domain/stock/Tag'

export const useGetTagsQuery = (params: TagListParams) => {
  const getTagsFn = useQuery<Tag[]>({
    queryKey: ['getTags', params],
    queryFn: () => getTags(params),
    enabled: params !== null,
  })

  return getTagsFn
}

export const useGetTagByIdQuery = (id: string) => {
  const getTagByIdFn = useQuery<Tag>({
    queryKey: ['getTagById', id],
    queryFn: () => getTagById(id),
    enabled: !!id,
  })

  return getTagByIdFn
}

export const usePostTagMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Omit<Tag, 'id'>) => postTag(params),
  })

  const postTagAsync = useCallback(
    async (props: Omit<Tag, 'id'>) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postTagAsync,
    loading: isPending,
  }
}

export const usePutTagMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Tag) => putTag(params),
  })

  const putTagAsync = useCallback(
    async (props: Tag) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    putTagAsync,
    loading: isPending,
  }
}

export const useDeleteTagMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteTag(id),
  })

  const deleteTagAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    deleteTagAsync,
    loading: isPending,
  }
}
