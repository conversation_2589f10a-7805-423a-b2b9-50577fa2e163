import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  postProduct,
  putProduct,
  getProducts,
  getProductById,
  deleteProduct,
  type ProductListParams,
  type Product,
} from '@app/domain/stock/Product'

export const useGetProductsQuery = (params: ProductListParams) => {
  const getProductsFn = useQuery<Product[]>({
    queryKey: ['getProducts', params],
    queryFn: () => getProducts(params),
    enabled: params !== null,
  })

  return getProductsFn
}

export const useGetProductByIdQuery = (id: string) => {
  const getProductByIdFn = useQuery<Product>({
    queryKey: ['getProductById', id],
    queryFn: () => getProductById(id),
    enabled: !!id,
  })

  return getProductByIdFn
}

export const usePostProductMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Omit<Product, 'id'>) => postProduct(params),
  })

  const postProductAsync = useCallback(
    async (props: Omit<Product, 'id'>) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postProductAsync,
    loading: isPending,
  }
}

export const usePutProductMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Product) => putProduct(params),
  })

  const putProductAsync = useCallback(
    async (props: Product) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    putProductAsync,
    loading: isPending,
  }
}

export const useDeleteProductMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteProduct(id),
  })

  const deleteProductAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    deleteProductAsync,
    loading: isPending,
  }
}
