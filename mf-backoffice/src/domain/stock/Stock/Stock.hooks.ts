import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  postStock,
  putStock,
  getStocks,
  getStockById,
  deleteStock,
  type StockListParams,
  type Stock,
} from '@app/domain/stock/Stock'
import { queryClient } from '@app/reactQuery'

export const useGetStocksQuery = (params: StockListParams) => {
  return useQuery<Stock[]>({
    queryKey: ['getStocks', params],
    queryFn: () => getStocks(params),
    enabled: params !== null,
  })
}

export const useGetStockByIdQuery = (id: string) => {
  return useQuery<Stock>({
    queryKey: ['getStockById', id],
    queryFn: () => getStockById(id),
    enabled: !!id,
  })
}

export const usePostStockMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Omit<Stock, 'id'>) => postStock(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getStocks'] })
    },
  })

  const postStockAsync = useCallback(
    async (props: Omit<Stock, 'id'>) => {
      const data = await mutateAsync(props)

      return data
    },
    [mutateAsync],
  )

  return {
    postStockAsync,
    loading: isPending,
  }
}

export const usePutStockMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Stock) => putStock(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['getStocks'] })
    },
  })

  const putStockAsync = useCallback(
    async (props: Stock) => {
      const data = await mutateAsync(props)

      return data
    },
    [mutateAsync],
  )

  return {
    putStockAsync,
    loading: isPending,
  }
}

export const useDeleteStockMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteStock(id),
  })

  const deleteStockAsync = useCallback(
    async (props: string) => {
      const data = await mutateAsync(props)

      return data
    },
    [mutateAsync],
  )

  return {
    deleteStockAsync,
    loading: isPending,
  }
}
