import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  postCategory,
  putCategory,
  getCategories,
  getCategoryById,
  deleteCategory,
  type CategoryListParams,
  type Category,
} from '@app/domain/stock/Category'

export const useGetCategoriesQuery = (params: CategoryListParams) => {
  const getCategoriesFn = useQuery<Category[]>({
    queryKey: ['getCategories', params],
    queryFn: () => getCategories(params),
    enabled: params !== null,
  })

  return getCategoriesFn
}

export const useGetCategoryByIdQuery = (id: string) => {
  const getCategoryByIdFn = useQuery<Category>({
    queryKey: ['getCategoryById', id],
    queryFn: () => getCategoryById(id),
    enabled: id !== null,
  })

  return getCategoryByIdFn
}

export const usePostCategoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Omit<Category, 'id'>) => postCategory(params),
  })

  const postCategoryAsync = useCallback(
    async (props: Omit<Category, 'id'>) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postCategoryAsync,
    loading: isPending,
  }
}

export const usePutCategoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Category) => putCategory(params),
  })

  const putCategoryAsync = useCallback(
    async (props: Category) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    putCategoryAsync,
    loading: isPending,
  }
}

export const useDeleteCategoryMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteCategory(id),
  })

  const deleteCategoryAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    deleteCategoryAsync,
    loading: isPending,
  }
}
