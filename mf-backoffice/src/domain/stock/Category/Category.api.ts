import { api, type ApiResponse } from '@app/domain/services'
import type { Category, CategoryListParams } from '@app/domain/stock/Category'

export const getCategories = async (params: CategoryListParams) => {
  const response = await api.get<ApiResponse<Category[]>>(
    `/stock/product-categories?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data?.data
}

export const getCategoryById = async (id: string) => {
  const response = await api.get<ApiResponse<Category>>(
    `/stock/product-categories/${id}`,
  )

  return response?.data?.data
}

export const postCategory = async (params: Omit<Category, 'id'>) => {
  const response = await api.post<ApiResponse<Category>>(
    '/stock/product-categories',
    params,
  )

  return response?.data?.data
}

export const putCategory = async (params: Category) => {
  const { id, ...body } = params

  const response = await api.put<ApiResponse<Category>>(
    `/stock/product-categories/${id}`,
    body,
  )

  return response?.data?.data
}

export const deleteCategory = async (id: string) => {
  const response = await api.delete<ApiResponse<void>>(
    `/stock/product-categories/${id}`,
  )

  return response?.data?.data
}
