import { useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
  postUser,
  putUser,
  getUsers,
  getUserById,
  deleteUser,
  type UserListParams,
  type User,
} from '@app/domain/User'

export const useGetUsersQuery = (params: UserListParams) => {
  const getUsersFn = useQuery<User[]>({
    queryKey: ['getUsers', params],
    queryFn: () => getUsers(params),
    enabled: params !== null,
  })

  return getUsersFn
}

export const useGetUserByIdQuery = (id: string) => {
  const getUserByIdFn = useQuery<User>({
    queryKey: ['getUserById', id],
    queryFn: () => getUserById(id),
    enabled: !!id,
  })

  return getUserByIdFn
}

export const usePostUserMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: Omit<User, 'id'>) => postUser(params),
  })

  const postUserAsync = useCallback(
    async (props: Omit<User, 'id'>) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postUserAsync,
    loading: isPending,
  }
}

export const usePutUserMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (params: User) => putUser(params),
  })

  const putUserAsync = useCallback(
    async (props: User) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    putUserAsync,
    loading: isPending,
  }
}

export const useDeleteUserMutation = () => {
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (id: string) => deleteUser(id),
  })

  const deleteUserAsync = useCallback(
    async (props: string) => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    deleteUserAsync,
    loading: isPending,
  }
}
