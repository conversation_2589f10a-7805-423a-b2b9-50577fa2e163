import { api, type ApiResponse } from '@app/domain/services'
import type { User, UserListParams } from '@app/domain/User'

export const getUsers = async (params: UserListParams) => {
  const response = await api.get<ApiResponse<User[]>>(
    `/user?page=${params.page}&perPage=${params.perPage}`,
  )

  return response?.data?.data
}

export const getUserById = async (id: string) => {
  const response = await api.get<ApiResponse<User>>(`/user/${id}`)

  return response?.data?.data
}

export const postUser = async (params: Omit<User, 'id'>) => {
  const response = await api.post<ApiResponse<User>>('/user', params)

  return response?.data?.data
}

export const putUser = async (params: User) => {
  const { id, ...body } = params

  const response = await api.put<ApiResponse<User>>(`/user/${id}`, body)

  return response?.data?.data
}

export const deleteUser = async (id: string) => {
  const response = await api.delete<ApiResponse<void>>(`/user/${id}`)

  return response?.data?.data
}
