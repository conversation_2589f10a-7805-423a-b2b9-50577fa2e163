import type { AxiosError } from 'axios'
import type { ApiResponse } from '@app/domain/services'

interface HandledError extends Error {
  isHandled?: boolean
}

export function handleApiError(
  error: unknown,
  showToast: (msg: string) => void,
) {
  if ((error as HandledError)?.isHandled) return

  if (typeof error === 'object' && error !== null && 'response' in error) {
    const axiosError = error as AxiosError<ApiResponse<unknown>>

    const apiMessage = axiosError.response?.data?.message || 'Erro inesperado'

    showToast(apiMessage)

    return
  }

  showToast('Erro desconhecido')
}
