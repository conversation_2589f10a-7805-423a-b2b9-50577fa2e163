import { Table } from '@thrift/design-system/packages/molecules/Table'
import { MoreActions } from '@thrift/design-system/packages/molecules/MoreActions'
import { useStock } from '@app/application/stock/Stock'
import { useLanguage } from '@app/application/Language'
import { useNavigate } from 'react-router-dom'

const Stock = () => {
  const { loadMore, isLoading, onDelete } = useStock()
  const { translate } = useLanguage()
  const navigate = useNavigate()

  return (
    <Table
      columns={[
        { header: translate('stock:location'), accessor: 'location' },
        { header: translate('stock:category'), accessor: 'category' },
        { header: translate('stock:quantity'), accessor: 'quantity' },
      ]}
      data={[]}
      onAdd={() => navigate('/stock/form')}
      onFilter={() => alert('Filtro clicado')}
      renderActions={(row) => (
        <MoreActions
          options={[
            {
              label: translate('titles.edit'),
              onClick: () => navigate(`/stock/form/${row.id}`),
            },
            {
              label: translate('titles.delete'),
              onClick: () => onDelete(row.id as string),
            },
          ]}
        />
      )}
      onEndReached={loadMore}
      isLoading={isLoading}
      labelEmpty={translate('stock:emptyTable')}
    />
  )
}

export { Stock }
