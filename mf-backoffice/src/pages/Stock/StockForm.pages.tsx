import { useStock } from '@app/application/stock/Stock'
import { useLanguage } from '@app/application/Language'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { Select } from '@thrift/design-system/packages/molecules/Select'
import { InputNumber } from '@thrift/design-system/packages/molecules/InputNumber'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { useNavigate } from 'react-router-dom'

const StockForm = () => {
  const { register, handleSubmit, errors, setValue, productId, categoryId } =
    useStock()

  const navigate = useNavigate()
  const { translate } = useLanguage()

  return (
    <form onSubmit={handleSubmit} className="flex flex-col h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputText
          label={translate('stock:location')}
          {...register('locationId')}
          errorMessage={errors.locationId?.message}
        />
        <Select
          label={translate('stock:product')}
          options={[
            { value: 'product1', label: translate('stock:product1') },
            { value: 'product2', label: translate('stock:product2') },
          ]}
          value={productId}
          onChange={(value: string) => setValue('productId', value)}
          errorMessage={errors.productId?.message}
          isSearchable={false}
        />
        <Select
          label={translate('stock:category')}
          options={[
            { value: 'category1', label: translate('stock:category1') },
            { value: 'category2', label: translate('stock:category2') },
          ]}
          value={categoryId}
          onChange={(value: string) => setValue('categoryId', value)}
          errorMessage={errors.categoryId?.message}
          isSearchable={false}
        />
        <InputNumber
          label={translate('stock:quantity')}
          {...register('quantity')}
          onChange={(value) => setValue('quantity', value as string)}
          errorMessage={errors.quantity?.message}
        />
      </div>
      <div className="flex-grow" />
      <div className="w-full flex justify-end">
        <div className="w-full max-w-[300px] flex flex-col md:flex-row gap-2">
          <Button variant="secondary" onClick={() => navigate(-1)}>
            Cancelar
          </Button>
          <Button type="submit" variant="primary">
            Salvar
          </Button>
        </div>
      </div>
    </form>
  )
}

export { StockForm }
