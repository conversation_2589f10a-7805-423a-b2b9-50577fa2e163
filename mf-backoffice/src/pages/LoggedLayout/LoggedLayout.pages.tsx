import {
  useLocation,
  Outlet,
  Link,
  useMatches,
  useParams,
  type UIMatch,
} from 'react-router-dom'
import { LoggedPage } from '@thrift/design-system/packages/organisms/LoggedPage'
import { useAuth } from '@app/application/Auth'
import { mockMenu } from '@app/pages/LoggedLayout'
import { useLanguage } from '@app/application/Language'

type PageTitle = string | Partial<Record<'create' | 'edit', string>>

export type PageHandle = {
  pageTitle?: PageTitle
  menuPath?: string
}

function resolvePageTitle(
  title: PageTitle | undefined,
  hasParams: boolean,
): string {
  if (!title) return ''
  if (typeof title === 'string') return title

  return hasParams
    ? (title.edit ?? title.create ?? '')
    : (title.create ?? title.edit ?? '')
}

export const LoggedLayout: React.FC = () => {
  const { translate } = useLanguage()
  const { onLogout } = useAuth()
  const location = useLocation()
  const routeParams = useParams()
  const matches = useMatches() as UIMatch<unknown, PageHandle>[]

  const currentPath = location.pathname

  const pageWithMenuPath = [...matches]
    .reverse()
    .find((m) => m.handle?.menuPath)

  const activePath = pageWithMenuPath?.handle?.menuPath ?? currentPath

  const pageMatch = [...matches].reverse().find((m) => m.handle?.pageTitle)
  const handleTitle = pageMatch?.handle?.pageTitle

  const hasIdParam = Object.values(routeParams).some(Boolean)
  const pageTitleKey = resolvePageTitle(handleTitle, hasIdParam)
  const translatedTitle = pageTitleKey ? translate(pageTitleKey) : ''

  return (
    <LoggedPage
      userMenu={{
        name: 'André Cordeiro',
        userFunction: 'Administrador',
        items: [
          {
            name: translate('titles:exit'),
            iconName: 'LogOut',
            onClick: () => onLogout(),
          },
        ],
      }}
      menus={mockMenu}
      activePath={activePath}
      LinkComponent={Link}
      pageTitle={translatedTitle}
    >
      <Outlet />
    </LoggedPage>
  )
}
