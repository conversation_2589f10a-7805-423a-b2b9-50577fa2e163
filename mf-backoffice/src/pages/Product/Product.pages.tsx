import { Table } from '@thrift/design-system/packages/molecules/Table'
import { MoreActions } from '@thrift/design-system/packages/molecules/MoreActions'
import { useLanguage } from '@app/application/Language'
import { useNavigate } from 'react-router-dom'
import { useProduct } from '@app/application/stock/Product'

const Product = () => {
  const { loadMore, isLoading, onDelete } = useProduct()
  const { translate } = useLanguage()
  const navigate = useNavigate()

  return (
    <Table
      columns={[
        { header: translate('product:code'), accessor: 'code' },
        { header: translate('product:name'), accessor: 'name' },
        { header: translate('product:category'), accessor: 'category' },
      ]}
      data={[]}
      onAdd={() => navigate('/product/form')}
      onFilter={() => alert('Filtro clicado')}
      renderActions={(row) => (
        <MoreActions
          options={[
            {
              label: translate('titles.edit'),
              onClick: () => navigate(`/product/form/${row.id}`),
            },
            {
              label: translate('titles.delete'),
              onClick: () => onDelete(row.id as string),
            },
          ]}
        />
      )}
      onEndReached={loadMore}
      isLoading={isLoading}
      labelEmpty={translate('product:emptyTable')}
    />
  )
}

export { Product }
