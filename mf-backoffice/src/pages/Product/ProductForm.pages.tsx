import { useLanguage } from '@app/application/Language'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { Select } from '@thrift/design-system/packages/molecules/Select'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { useNavigate } from 'react-router-dom'
import { useProduct } from '@app/application/stock/Product'
import { FileUpload } from '@thrift/design-system/packages/molecules/FileUpload'

const ProductForm = () => {
  const { register, handleSubmit, errors, setValue, gridId, categoryId } =
    useProduct()

  const navigate = useNavigate()
  const { translate } = useLanguage()

  return (
    <form onSubmit={handleSubmit} className="flex flex-col h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputText
          label={translate('product:name')}
          {...register('name')}
          errorMessage={errors.name?.message}
        />
        <InputText
          label={translate('product:description')}
          {...register('description')}
          errorMessage={errors.description?.message}
        />
        <InputText
          label={translate('product:code')}
          {...register('code')}
          errorMessage={errors.code?.message}
        />
        <Select
          label={translate('product:category')}
          options={[
            { value: 'category1', label: translate('product:category1') },
            { value: 'category2', label: translate('product:category2') },
          ]}
          value={categoryId}
          onChange={(value: string) => setValue('categoryId', value)}
          errorMessage={errors.categoryId?.message}
          isSearchable={false}
        />
        <Select
          label={translate('product:grid')}
          options={[
            { value: 'grid1', label: translate('product:grid1') },
            { value: 'grid2', label: translate('product:grid2') },
          ]}
          value={gridId}
          onChange={(value: string) => setValue('gridId', value)}
          errorMessage={errors.gridId?.message}
          isSearchable={false}
        />
        <FileUpload
          label={translate('product:image')}
          chooseFileText={translate('product:chooseFile')}
          noFileText={translate('product:noFile')}
          labelRemove={translate('product:removeFile')}
          labelSelectedFiles={translate('product:selectedFiles')}
          accept="image/*"
          maxFiles={2}
          onChange={(files) => setValue('image', files)}
          errorMessage={errors.image?.message}
        />
      </div>
      <div className="flex-grow" />
      <div className="w-full flex justify-end">
        <div className="w-full max-w-[300px] flex flex-col md:flex-row gap-2">
          <Button variant="secondary" onClick={() => navigate(-1)}>
            Cancelar
          </Button>
          <Button type="submit" variant="primary">
            Salvar
          </Button>
        </div>
      </div>
    </form>
  )
}

export { ProductForm }
