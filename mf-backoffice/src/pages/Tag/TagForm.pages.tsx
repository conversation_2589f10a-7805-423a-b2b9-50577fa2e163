import { useLanguage } from '@app/application/Language'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { useNavigate } from 'react-router-dom'
import { useTag } from '@app/application/stock/Tag'

const TagForm = () => {
  const { register, handleSubmit, errors } = useTag()

  const navigate = useNavigate()
  const { translate } = useLanguage()

  return (
    <form onSubmit={handleSubmit} className="flex flex-col h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputText
          label={translate('tag:name')}
          {...register('name')}
          errorMessage={errors.name?.message}
        />
      </div>
      <div className="flex-grow" />
      <div className="w-full flex justify-end">
        <div className="w-full max-w-[300px] flex flex-col md:flex-row gap-2">
          <Button variant="secondary" onClick={() => navigate(-1)}>
            Cancelar
          </Button>
          <Button type="submit" variant="primary">
            Salvar
          </Button>
        </div>
      </div>
    </form>
  )
}

export { TagForm }
