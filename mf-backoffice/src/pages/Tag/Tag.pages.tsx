import { Table } from '@thrift/design-system/packages/molecules/Table'
import { MoreActions } from '@thrift/design-system/packages/molecules/MoreActions'
import { useLanguage } from '@app/application/Language'
import { useNavigate } from 'react-router-dom'
import { useTag } from '@app/application/stock/Tag'

const Tag = () => {
  const { loadMore, isLoading, onDelete } = useTag()
  const { translate } = useLanguage()
  const navigate = useNavigate()

  return (
    <Table
      columns={[{ header: translate('tag:name'), accessor: 'name' }]}
      data={[]}
      onAdd={() => navigate('/tags/form')}
      onFilter={() => alert('Filtro clicado')}
      renderActions={(row) => (
        <MoreActions
          options={[
            {
              label: translate('titles.edit'),
              onClick: () => navigate(`/tags/form/${row.id}`),
            },
            {
              label: translate('titles.delete'),
              onClick: () => onDelete(row.id as string),
            },
          ]}
        />
      )}
      onEndReached={loadMore}
      isLoading={isLoading}
      labelEmpty={translate('tag:emptyTable')}
    />
  )
}

export { Tag }
