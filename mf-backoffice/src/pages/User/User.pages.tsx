import { Table } from '@thrift/design-system/packages/molecules/Table'
import { MoreActions } from '@thrift/design-system/packages/molecules/MoreActions'
import { useLanguage } from '@app/application/Language'
import { useNavigate } from 'react-router-dom'
import { useUser } from '@app/application/User'

const User = () => {
  const { loadMore, isLoading, onDelete } = useUser()
  const { translate } = useLanguage()
  const navigate = useNavigate()

  return (
    <Table
      columns={[
        { header: translate('user:name'), accessor: 'name' },
        { header: translate('user:status'), accessor: 'status' },
        { header: translate('user:email'), accessor: 'email' },
        { header: translate('user:role'), accessor: 'role' },
      ]}
      data={[]}
      onAdd={() => navigate('/users/form')}
      onFilter={() => alert('Filtro clicado')}
      renderActions={(row) => (
        <MoreActions
          options={[
            {
              label: translate('titles.edit'),
              onClick: () => navigate(`/users/form/${row.id}`),
            },
            {
              label: translate('titles.delete'),
              onClick: () => onDelete(row.id as string),
            },
          ]}
        />
      )}
      onEndReached={loadMore}
      isLoading={isLoading}
      labelEmpty={translate('user:emptyTable')}
    />
  )
}

export { User }
