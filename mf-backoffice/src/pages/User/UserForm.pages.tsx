import { useLanguage } from '@app/application/Language'
import { Tabs } from '@thrift/design-system/packages/molecules/Tabs'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { useNavigate } from 'react-router-dom'
import { useUser } from '@app/application/User'
import { InputEmail } from '@thrift/design-system/packages/molecules/InputEmail'
import { Select } from '@thrift/design-system/packages/molecules/Select'

import { Checkbox } from '@thrift/design-system/packages/molecules/Checkbox'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'

const UserForm = () => {
  const {
    register,
    handleSubmit,
    errors,
    index,
    setIndex,
    roleId,
    status,
    setValue,
  } = useUser()

  const navigate = useNavigate()
  const { translate } = useLanguage()

  return (
    <div onSubmit={handleSubmit} className="flex flex-col h-full">
      <Tabs
        tabs={[
          {
            label: translate('user:access'),
            content: (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputEmail
                  {...register('email')}
                  label={translate('user:email')}
                  errorMessage={errors.email?.message}
                />
                <Select
                  label={translate('user:role')}
                  options={[
                    { value: 'role1', label: translate('user:role1') },
                    { value: 'role2', label: translate('user:role2') },
                  ]}
                  value={roleId}
                  onChange={(value: string) => setValue('roleId', value)}
                  errorMessage={errors.roleId?.message}
                  isSearchable={false}
                />
                <Checkbox
                  value={''}
                  checked={status}
                  label={
                    status
                      ? translate('user:active')
                      : translate('user:inactive')
                  }
                  description={translate('user:statusDescription')}
                  onChange={(checked: boolean) => setValue('status', checked)}
                  name={'status'}
                />
              </div>
            ),
          },
          {
            label: translate('user:personalInformation'),
            content: (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputText
                  label={translate('tag:name')}
                  {...register('name')}
                  errorMessage={errors.name?.message}
                />
                <InputText
                  label={translate('user:lastName')}
                  {...register('lastName')}
                  errorMessage={errors.lastName?.message}
                />
              </div>
            ),
          },
        ]}
        activeIndex={index}
        onChange={setIndex}
      />
      <div className="flex-grow" />
      <div className="w-full flex justify-end">
        <div className="w-full max-w-[300px] flex flex-col md:flex-row gap-2">
          <Button variant="secondary" onClick={() => navigate(-1)}>
            Cancelar
          </Button>
          <Button variant="primary" onClick={handleSubmit}>
            Salvar
          </Button>
        </div>
      </div>
    </div>
  )
}

export { UserForm }
