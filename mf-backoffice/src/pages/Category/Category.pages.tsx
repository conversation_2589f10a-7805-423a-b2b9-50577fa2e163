import { Table } from '@thrift/design-system/packages/molecules/Table'
import { MoreActions } from '@thrift/design-system/packages/molecules/MoreActions'
import { useLanguage } from '@app/application/Language'
import { useNavigate } from 'react-router-dom'
import { useCategory } from '@app/application/stock/Category'

const Category = () => {
  const { loadMore, isLoading, onDelete } = useCategory()
  const { translate } = useLanguage()
  const navigate = useNavigate()

  return (
    <Table
      columns={[
        { header: translate('category:name'), accessor: 'name' },
        { header: translate('category:description'), accessor: 'description' },
        {
          header: translate('category:mainCategory'),
          accessor: 'mainCategory',
        },
        { header: translate('category:helpText'), accessor: 'helpText' },
      ]}
      data={[]}
      onAdd={() => navigate('/categories/form')}
      onFilter={() => alert('Filtro clicado')}
      renderActions={(row) => (
        <MoreActions
          options={[
            {
              label: translate('titles.edit'),
              onClick: () => navigate(`/categories/form/${row.id}`),
            },
            {
              label: translate('titles.delete'),
              onClick: () => onDelete(row.id as string),
            },
          ]}
        />
      )}
      onEndReached={loadMore}
      isLoading={isLoading}
      labelEmpty={translate('category:emptyTable')}
    />
  )
}

export { Category }
