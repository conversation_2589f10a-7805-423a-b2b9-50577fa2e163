import { useLanguage } from '@app/application/Language'
import { InputText } from '@thrift/design-system/packages/molecules/InputText'
import { InputTextArea } from '@thrift/design-system/packages/molecules/InputTextArea'
import { Select } from '@thrift/design-system/packages/molecules/Select'
import { Button } from '@thrift/design-system/packages/molecules/Button'
import { useNavigate } from 'react-router-dom'
import { useCategory } from '@app/application/stock/Category'

const CategoryForm = () => {
  const { register, handleSubmit, errors, setValue, mainCategoryId } =
    useCategory()

  const navigate = useNavigate()
  const { translate } = useLanguage()

  return (
    <form onSubmit={handleSubmit} className="flex flex-col h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputText
          label={translate('category:name')}
          {...register('name')}
          errorMessage={errors.name?.message}
        />
        <InputText
          label={translate('category:description')}
          {...register('description')}
          errorMessage={errors.description?.message}
        />
        <Select
          label={translate('category:mainCategory')}
          options={[
            { value: 'category1', label: translate('category:category1') },
            { value: 'category2', label: translate('category:category2') },
          ]}
          value={mainCategoryId}
          onChange={(value: string) => setValue('mainCategoryId', value)}
          errorMessage={errors.mainCategoryId?.message}
          isSearchable={false}
        />
        <InputTextArea
          label={translate('category:helpText')}
          {...register('helpText')}
          errorMessage={errors.helpText?.message}
        />
      </div>
      <div className="flex-grow" />
      <div className="w-full flex justify-end">
        <div className="w-full max-w-[300px] flex flex-col md:flex-row gap-2">
          <Button variant="secondary" onClick={() => navigate(-1)}>
            Cancelar
          </Button>
          <Button type="submit" variant="primary">
            Salvar
          </Button>
        </div>
      </div>
    </form>
  )
}

export { CategoryForm }
