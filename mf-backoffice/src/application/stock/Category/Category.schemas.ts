import { z } from 'zod'
import { useLanguage } from '@app/application/Language'
import { InputTextSchema } from '@thrift/design-system/packages/molecules/InputText'
import { InputTextAreaSchema } from '@thrift/design-system/packages/molecules/InputTextArea'

export const useCategorySchema = () => {
  const { translate } = useLanguage()

  return z.object({
    name: InputTextSchema({
      max: { value: 50, message: translate('category:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('category:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('category:fieldRequired') },
    }),
    description: InputTextSchema({
      max: { value: 50, message: translate('category:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('category:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('category:fieldRequired') },
    }),
    mainCategoryId: z.string().nonempty(translate('category:fieldRequired')),
    helpText: InputTextAreaSchema({
      max: {
        value: 200,
        message: translate('category:fieldMax', { max: 200 }),
      },
      min: { value: 0, message: translate('category:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('category:fieldRequired') },
    }),
  })
}

export type UseCategorySchemaType = z.infer<
  ReturnType<typeof useCategorySchema>
>
