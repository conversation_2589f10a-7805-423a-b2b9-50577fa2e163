import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'
import { useEffect, useState } from 'react'
import {
  type UseCategorySchemaType,
  useCategorySchema,
} from '@app/application/stock/Category'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams } from 'react-router-dom'
import {
  useDeleteCategoryMutation,
  useGetCategoryByIdQuery,
  useGetCategoriesQuery,
  usePostCategoryMutation,
  usePutCategoryMutation,
  type Category,
} from '@app/domain/stock/Category'
import { useAuthStore } from '@app/domain/stores/Auth'

export const useCategory = () => {
  const { id } = useParams()
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const CategorySchema = useCategorySchema()
  const navigate = useNavigate()
  const [page, setPage] = useState(1)
  const [items, setItems] = useState<Category[]>([])
  const tenant = useAuthStore((state) => state.tenant)
  const { postCategoryAsync } = usePostCategoryMutation()
  const { putCategoryAsync } = usePutCategoryMutation()
  const { deleteCategoryAsync } = useDeleteCategoryMutation()

  const { data: categoryData, isLoading: isLoadingCategory } =
    useGetCategoryByIdQuery(id ?? '')

  const { data, isLoading } = useGetCategoriesQuery({
    id: tenant,
    page,
    perPage: 10,
  })

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch: getValues,
    reset,
  } = useForm<UseCategorySchemaType>({
    resolver: zodResolver(CategorySchema),
    defaultValues: {
      name: '',
      description: '',
      mainCategoryId: '',
      helpText: '',
    },
  })

  const { mainCategoryId } = getValues()

  useEffect(() => {
    if (data && Array.isArray(data)) {
      setItems((prev) => [...prev, ...data])
    }
  }, [data])

  useEffect(() => {
    if (categoryData) {
      reset({
        name: categoryData.name ?? '',
        description: categoryData.description ?? '',
        mainCategoryId: categoryData.mainCategoryId ?? '',
        helpText: categoryData.helpText ?? '',
      })
    }
  }, [categoryData, reset])

  const onSubmitForm = async (formData: UseCategorySchemaType) => {
    try {
      if (id) {
        await putCategoryAsync({ ...formData, id })
      } else {
        await postCategoryAsync(formData)
      }

      showToast({
        type: 'success',
        message: translate('category:formSubmitted'),
      })

      navigate('/category')
    } catch {
      showToast({
        type: 'error',
        message: translate('category:errorSubmitting'),
      })
    }
  }

  const loadMore = () => {
    const nextPage = page + 1

    setPage(nextPage)
  }

  const onDelete = async (id: string) => {
    try {
      await deleteCategoryAsync(id)
      setItems((prev) => prev.filter((item) => item.id !== id))
      showToast({
        type: 'success',
        message: translate('category:deleted'),
      })
    } catch {
      showToast({
        type: 'error',
        message: translate('category:errorDeleting'),
      })
    }
  }

  return {
    loadMore,
    isLoading: isLoading || isLoadingCategory,
    register,
    handleSubmit: handleSubmit(onSubmitForm),
    errors,
    setValue,
    mainCategoryId,
    items,
    onDelete,
  }
}
