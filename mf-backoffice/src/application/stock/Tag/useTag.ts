import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'
import { useEffect, useState } from 'react'
import { type UseTagSchemaType, useTagSchema } from '@app/application/stock/Tag'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams } from 'react-router-dom'
import {
  useDeleteTagMutation,
  useGetTagByIdQuery,
  useGetTagsQuery,
  usePostTagMutation,
  usePutTagMutation,
  type Tag,
} from '@app/domain/stock/Tag'
import { useAuthStore } from '@app/domain/stores/Auth'

export const useTag = () => {
  const { id } = useParams()
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const TagSchema = useTagSchema()
  const navigate = useNavigate()
  const [page, setPage] = useState(1)
  const [items, setItems] = useState<Tag[]>([])
  const tenant = useAuthStore((state) => state.tenant)
  const { postTagAsync } = usePostTagMutation()
  const { putTagAsync } = usePutTagMutation()
  const { deleteTagAsync } = useDeleteTagMutation()

  const { data: tagData, isLoading: isLoadingTag } = useGetTagByIdQuery(
    id ?? '',
  )

  const { data, isLoading } = useGetTagsQuery({
    id: tenant,
    page,
    perPage: 10,
  })

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UseTagSchemaType>({
    resolver: zodResolver(TagSchema),
    defaultValues: {
      name: '',
    },
  })

  useEffect(() => {
    if (data && Array.isArray(data)) {
      setItems((prev) => [...prev, ...data])
    }
  }, [data])

  useEffect(() => {
    if (tagData) {
      reset({
        name: tagData.name ?? '',
      })
    }
  }, [tagData, reset])

  const onSubmitForm = async (formData: UseTagSchemaType) => {
    try {
      if (id) {
        await putTagAsync({ ...formData, id })
      } else {
        await postTagAsync(formData)
      }

      showToast({
        type: 'success',
        message: translate('tag:formSubmitted'),
      })

      navigate('/tags')
    } catch {
      showToast({
        type: 'error',
        message: translate('tag:errorSubmitting'),
      })
    }
  }

  const loadMore = () => {
    const nextPage = page + 1

    setPage(nextPage)
  }

  const onDelete = async (id: string) => {
    try {
      await deleteTagAsync(id)
      setItems((prev) => prev.filter((item) => item.id !== id))
      showToast({
        type: 'success',
        message: translate('tag:deleted'),
      })
    } catch {
      showToast({
        type: 'error',
        message: translate('tag:errorDeleting'),
      })
    }
  }

  return {
    loadMore,
    isLoading: isLoading || isLoadingTag,
    register,
    handleSubmit: handleSubmit(onSubmitForm),
    errors,
    items,
    onDelete,
  }
}
