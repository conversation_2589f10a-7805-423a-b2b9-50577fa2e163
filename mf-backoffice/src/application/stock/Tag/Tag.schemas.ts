import { z } from 'zod'
import { useLanguage } from '@app/application/Language'
import { InputTextSchema } from '@thrift/design-system/packages/molecules/InputText'

export const useTagSchema = () => {
  const { translate } = useLanguage()

  return z.object({
    name: InputTextSchema({
      max: { value: 50, message: translate('tag:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('tag:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('tag:fieldRequired') },
    }),
  })
}

export type UseTagSchemaType = z.infer<ReturnType<typeof useTagSchema>>
