import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'
import { useEffect, useState } from 'react'
import { type UseUserSchemaType, useUserSchema } from '@app/application/User'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams } from 'react-router-dom'
import {
  useDeleteUserMutation,
  useGetUserByIdQuery,
  useGetUsersQuery,
  usePostUserMutation,
  usePutUserMutation,
  type User,
} from '@app/domain/User'
import { useAuthStore } from '@app/domain/stores/Auth'

export const useUser = () => {
  const { id } = useParams()
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const UserSchema = useUserSchema()
  const navigate = useNavigate()
  const [page, setPage] = useState(1)
  const [items, setItems] = useState<User[]>([])
  const tenant = useAuthStore((state) => state.tenant)
  const { postUserAsync } = usePostUserMutation()
  const { putUserAsync } = usePutUserMutation()
  const { deleteUserAsync } = useDeleteUserMutation()
  const [index, setIndex] = useState(0)

  const { data: userData, isLoading: isLoadingUser } = useGetUserByIdQuery(
    id ?? '',
  )

  const { data, isLoading } = useGetUsersQuery({
    id: tenant,
    page,
    perPage: 10,
  })

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch: getValues,
    reset,
  } = useForm<UseUserSchemaType>({
    resolver: zodResolver(UserSchema),
    defaultValues: {
      name: '',
      lastName: '',
      roleId: '',
      email: '',
      status: true,
    },
  })

  const { roleId, status } = getValues()

  useEffect(() => {
    if (data && Array.isArray(data)) {
      setItems((prev) => [...prev, ...data])
    }
  }, [data])

  useEffect(() => {
    if (userData) {
      reset({
        name: userData.name ?? '',
        lastName: userData.lastName ?? '',
        email: userData.email ?? '',
        roleId: userData.roleId ?? '',
        status: userData.status ?? true,
      })
    }
  }, [userData, reset])

  const onSubmitForm = async (formData: UseUserSchemaType) => {
    try {
      if (id) {
        await putUserAsync({ ...formData, id })
      } else {
        await postUserAsync(formData)
      }

      showToast({
        type: 'success',
        message: translate('user:formSubmitted'),
      })

      navigate('/users')
    } catch {
      showToast({
        type: 'error',
        message: translate('user:errorSubmitting'),
      })
    }
  }

  const loadMore = () => {
    const nextPage = page + 1

    setPage(nextPage)
  }

  const onDelete = async (id: string) => {
    try {
      await deleteUserAsync(id)
      setItems((prev) => prev.filter((item) => item.id !== id))
      showToast({
        type: 'success',
        message: translate('user:deleted'),
      })
    } catch {
      showToast({
        type: 'error',
        message: translate('user:errorDeleting'),
      })
    }
  }

  return {
    loadMore,
    isLoading: isLoading || isLoadingUser,
    register,
    handleSubmit: handleSubmit(onSubmitForm),
    errors,
    items,
    onDelete,
    index,
    setIndex,
    roleId,
    setValue,
    status,
  }
}
