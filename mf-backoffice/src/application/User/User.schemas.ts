import { z } from 'zod'
import { useLanguage } from '@app/application/Language'
import { InputTextSchema } from '@thrift/design-system/packages/molecules/InputText'
import { InputEmailSchema } from '@thrift/design-system/packages/molecules/InputEmail'

export const useUserSchema = () => {
  const { translate } = useLanguage()

  return z.object({
    name: InputTextSchema({
      max: { value: 50, message: translate('user:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('user:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('user:fieldRequired') },
    }),
    lastName: InputTextSchema({
      max: { value: 50, message: translate('user:fieldMax', { max: 50 }) },
      min: { value: 0, message: translate('user:fieldMin', { min: 0 }) },
      required: { value: true, message: translate('user:fieldRequired') },
    }),
    email: InputEmailSchema({
      max: { value: 50, message: translate('user:fieldMax', { max: 50 }) },
      min: { value: 5, message: translate('user:fieldMin', { min: 5 }) },
      required: { value: true, message: translate('user:fieldRequired') },
      emailMessage: translate('user:emailInvalid'),
    }),
    roleId: z.string().nonempty(translate('user:fieldRequired')),
    status: z.boolean(),
  })
}

export type UseUserSchemaType = z.infer<ReturnType<typeof useUserSchema>>
