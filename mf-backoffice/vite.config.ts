import { resolve } from 'path'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import configs from './app.config.json'

const rootPath = resolve(__dirname, '.')
const sourcePath = resolve(rootPath, './src')

// https://vite.dev/config/
export default defineConfig({
  base: '/backoffice',
  root: rootPath,
  plugins: [react(), tailwindcss()],
  build: {
    sourcemap: true,
    emptyOutDir: true,
    outDir: resolve(rootPath, configs.build.outDir),
  },
  resolve: {
    alias: {
      '@app': sourcePath,
      'app.config.json': resolve(rootPath, 'app.config.json'),
    },
  },
  define: {
    'import.meta.env.APP_CONFIG_CHARSET': JSON.stringify(configs.app.charset),
    'import.meta.env.APP_CONFIG_LANG': JSON.stringify(configs.app.lang),
    'import.meta.env.APP_CONFIG_TITLE': JSON.stringify(configs.app.title),
  },
})
