#!/bin/bash
set -e

echo "🧪 Testing unified development mode with API Gateway..."

# Test the development mode status first
echo "📋 Checking current development status..."
./simulator/thrift-cli-optimized --dev-status

echo ""
echo "🚀 Testing development mode startup..."
echo "This will start all services with the API gateway for consistent URLs"
echo ""

# Start development mode in background
echo "Starting development services..."
./simulator/thrift-cli-optimized --dev &
DEV_PID=$!

echo "⏳ Waiting 60 seconds for all services to start..."
sleep 60

# Check if services are running
echo "📋 Checking service status..."
./simulator/thrift-cli-optimized --dev-status

echo ""
echo "🌐 Testing API Gateway endpoints..."

# Test API Gateway health
if curl -s -f "http://localhost:8080/health" >/dev/null 2>&1; then
    echo "✅ API Gateway is responding"
else
    echo "❌ API Gateway is not responding"
fi

# Test if we can reach services through the gateway
echo "🔍 Testing service endpoints through API Gateway..."

# Test account service
if curl -s -f "http://localhost:8080/account/health" >/dev/null 2>&1; then
    echo "✅ Account service accessible via: http://localhost:8080/account/"
else
    echo "⚠️  Account service may still be starting up"
fi

# Test person service  
if curl -s -f "http://localhost:8080/person/health" >/dev/null 2>&1; then
    echo "✅ Person service accessible via: http://localhost:8080/person/"
else
    echo "⚠️  Person service may still be starting up"
fi

# Test wallet service
if curl -s -f "http://localhost:8080/wallet/health" >/dev/null 2>&1; then
    echo "✅ Wallet service accessible via: http://localhost:8080/wallet/"
else
    echo "⚠️  Wallet service may still be starting up"
fi

echo ""
echo "🎉 Development mode test completed!"
echo ""
echo "📋 Summary:"
echo "✨ Same URLs as production:"
echo "   - http://localhost:8080/account/authenticate"
echo "   - http://localhost:8080/person/..."
echo "   - http://localhost:8080/wallet/..."
echo "   - http://localhost:8080/stock/..."
echo "   - http://localhost:8080/evaluation/..."
echo ""
echo "🖥️  Frontend: http://localhost:5173"
echo ""
echo "🛑 To stop all services:"
echo "   ./simulator/thrift-cli-optimized --dev-stop"

# Stop the background process
kill $DEV_PID 2>/dev/null || true
