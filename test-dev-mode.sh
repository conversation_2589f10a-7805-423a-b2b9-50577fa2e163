#!/bin/bash
set -e

echo "🧪 Testing development mode setup..."

# Test 1: Check if local repositories exist
echo "📁 Checking local repositories..."
if [ -d "simulator/.cache/local-repos/ms-account" ]; then
    echo "✅ Local repositories found"
else
    echo "❌ Local repositories not found. Run: ./simulator/scripts/setup-local-repos.sh"
    exit 1
fi

# Test 2: Check Docker network
echo "🌐 Checking Docker network..."
if docker network ls | grep -q "thrift-network"; then
    echo "✅ Docker network exists"
else
    echo "⚠️  Creating Docker network..."
    docker network create thrift-network
fi

# Test 3: Check yarn cache volume
echo "📦 Checking yarn cache volume..."
if docker volume ls | grep -q "yarn-cache"; then
    echo "✅ Yarn cache volume exists"
else
    echo "⚠️  Creating yarn cache volume..."
    docker volume create yarn-cache
fi

# Test 4: Test development container startup
echo "🚀 Testing development container startup..."
echo "Starting ms-account in development mode for 10 seconds..."

# Start the container in background
APP_NAME="ms-account" docker compose \
    --project-name "thrift-dev-test" \
    --file "simulator/docker/macro-services/macro-service-dev-docker-compose.yml" \
    run --rm --detach \
    --name "ms-account-dev-test" \
    macro-service-dev > /dev/null &

CONTAINER_PID=$!

# Wait a bit for container to start
sleep 5

# Check if container is running
if docker ps | grep -q "ms-account-dev-test"; then
    echo "✅ Development container started successfully"
    
    # Show container logs
    echo "📋 Container logs (last 10 lines):"
    docker logs ms-account-dev-test --tail 10 2>/dev/null || echo "No logs yet"
    
    # Stop the container
    echo "🛑 Stopping test container..."
    docker stop ms-account-dev-test > /dev/null 2>&1 || true
    
    echo "✅ Development mode test completed successfully!"
    echo ""
    echo "🎉 You can now use:"
    echo "   ./simulator/thrift-cli-optimized --dev         # Start all services in dev mode"
    echo "   ./simulator/thrift-cli-optimized --dev-status  # Check dev services status"
    echo "   ./simulator/thrift-cli-optimized --dev-stop    # Stop all dev services"
    
else
    echo "❌ Development container failed to start"
    echo "Check Docker logs for more information"
    exit 1
fi
