import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateEvaluationProposalDto,
  EvaluationProposalModel,
  FetchEvaluationProposalsProps,
  UpdateEvaluationProposalDto,
} from '@app/domain/database/EvaluationProposal'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class EvaluationProposal {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<EvaluationProposalModel> {
    const evaluationProposal = await this.prisma.evaluationProposal.findFirst({
      where: { id, deleted: false },
    })

    if (!evaluationProposal) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'evaluation proposal' }),
      )
    }

    return evaluationProposal
  }

  private buildDateFilter(fromDate?: Date, toDate?: Date) {
    if (fromDate && toDate) {
      return {
        proposalDate: {
          gte: fromDate,
          lte: toDate,
        },
      }
    }

    if (fromDate) {
      return {
        proposalDate: {
          gte: fromDate,
        },
      }
    }

    if (toDate) {
      return {
        proposalDate: {
          lte: toDate,
        },
      }
    }

    return {}
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    evaluationBatchId,
    status,
    fromDate,
    toDate,
  }: FetchEvaluationProposalsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(evaluationBatchId && { evaluationBatchId }),
      ...(status && { status }),
      ...this.buildDateFilter(fromDate, toDate),
      ...(search && {
        OR: [
          { status: { contains: search } },
          { supplierNotes: { contains: search } },
        ],
      }),
    }

    const [evaluationProposals, totalItems] = await this.prisma.$transaction([
      this.prisma.evaluationProposal.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        include: {
          evaluationBatch: {
            select: {
              id: true,
              displayCode: true,
              status: true,
              supplierId: true,
            },
          },
        },
      }),
      this.prisma.evaluationProposal.count({ where }),
    ])

    return {
      items: evaluationProposals,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    evaluationBatchId,
    totalProposedValue,
    proposalDate = new Date(),
    status,
    supplierResponseDate,
    supplierNotes,
  }: CreateEvaluationProposalDto): Promise<EvaluationProposalModel> {
    const evaluationProposal = await this.prisma.evaluationProposal.create({
      data: {
        evaluationBatch: {
          connect: { id: evaluationBatchId },
        },
        totalProposedValue,
        proposalDate,
        status,
        supplierResponseDate,
        supplierNotes,
      },
    })

    return evaluationProposal
  }

  public async update({
    id,
    totalProposedValue,
    proposalDate,
    status,
    supplierResponseDate,
    supplierNotes,
  }: UpdateEvaluationProposalDto): Promise<EvaluationProposalModel> {
    // First check if evaluation proposal exists
    await this.findById(id)

    // Build the update data object with only the fields that are provided
    const updateData: Record<string, unknown> = {}

    if (totalProposedValue !== undefined)
      updateData.totalProposedValue = totalProposedValue
    if (proposalDate !== undefined) updateData.proposalDate = proposalDate
    if (status !== undefined) updateData.status = status
    if (supplierResponseDate !== undefined)
      updateData.supplierResponseDate = supplierResponseDate
    if (supplierNotes !== undefined) updateData.supplierNotes = supplierNotes

    const evaluationProposal = await this.prisma.evaluationProposal.update({
      where: { id },
      data: updateData,
    })

    return evaluationProposal
  }

  public async delete(id: string): Promise<EvaluationProposalModel> {
    // First check if evaluation proposal exists
    await this.findById(id)

    const evaluationProposal = await this.prisma.evaluationProposal.update({
      where: { id },
      data: { deleted: true },
    })

    return evaluationProposal
  }

  public async findByBatchId(
    evaluationBatchId: string,
  ): Promise<EvaluationProposalModel[]> {
    return this.prisma.evaluationProposal.findMany({
      where: { evaluationBatchId, deleted: false },
      orderBy: { proposalDate: 'desc' },
    })
  }
}
