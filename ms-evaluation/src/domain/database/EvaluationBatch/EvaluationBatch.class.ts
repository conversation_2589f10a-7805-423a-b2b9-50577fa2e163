import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateEvaluationBatchDto,
  EvaluationBatchModel,
  FetchEvaluationBatchesProps,
  UpdateEvaluationBatchDto,
} from '@app/domain/database/EvaluationBatch'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class EvaluationBatch {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<EvaluationBatchModel> {
    const evaluationBatch = await this.prisma.evaluationBatch.findFirst({
      where: { id, deleted: false },
    })

    if (!evaluationBatch) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'evaluation batch' }),
      )
    }

    return evaluationBatch
  }

  public async findByDisplayCode(
    displayCode: string,
  ): Promise<EvaluationBatchModel | null> {
    return this.prisma.evaluationBatch.findFirst({
      where: { displayCode, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    supplierId,
    storeId,
    status,
    displayCode,
  }: FetchEvaluationBatchesProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(supplierId && { supplierId }),
      ...(storeId && { storeId }),
      ...(status && { status }),
      ...(displayCode && { displayCode }),
      ...(search && {
        OR: [
          { displayCode: { contains: search } },
          { notes: { contains: search } },
          { status: { contains: search } },
        ],
      }),
    }

    const [evaluationBatches, totalItems] = await this.prisma.$transaction([
      this.prisma.evaluationBatch.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        include: {
          items: {
            where: { deleted: false },
          },
          proposals: {
            where: { deleted: false },
          },
        },
      }),
      this.prisma.evaluationBatch.count({ where }),
    ])

    return {
      items: evaluationBatches,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    supplierId,
    storeId,
    status,
    displayCode,
    notes,
  }: CreateEvaluationBatchDto): Promise<EvaluationBatchModel> {
    // Check if display code already exists
    const existingBatch = await this.findByDisplayCode(displayCode)

    if (existingBatch) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'evaluation batch',
          field: 'displayCode',
        }),
      )
    }

    const evaluationBatch = await this.prisma.evaluationBatch.create({
      data: {
        supplierId,
        storeId,
        status,
        displayCode,
        notes,
      },
    })

    return evaluationBatch
  }

  public async update({
    id,
    supplierId,
    storeId,
    status,
    displayCode,
    notes,
  }: UpdateEvaluationBatchDto): Promise<EvaluationBatchModel> {
    // First check if evaluation batch exists
    await this.findById(id)

    // Check if display code conflicts with another batch
    if (displayCode) {
      const existingBatch = await this.findByDisplayCode(displayCode)

      if (existingBatch && existingBatch.id !== id) {
        throw new ConflictException(
          i18next.t('common:conflict', {
            entity: 'evaluation batch',
            field: 'displayCode',
          }),
        )
      }
    }

    // Build the update data object with only the fields that are provided
    const updateData: Record<string, unknown> = {}

    if (supplierId !== undefined) updateData.supplierId = supplierId
    if (storeId !== undefined) updateData.storeId = storeId
    if (status !== undefined) updateData.status = status
    if (displayCode !== undefined) updateData.displayCode = displayCode
    if (notes !== undefined) updateData.notes = notes

    const evaluationBatch = await this.prisma.evaluationBatch.update({
      where: { id },
      data: updateData,
    })

    return evaluationBatch
  }

  public async delete(id: string): Promise<EvaluationBatchModel> {
    // First check if evaluation batch exists
    await this.findById(id)

    const evaluationBatch = await this.prisma.evaluationBatch.update({
      where: { id },
      data: { deleted: true },
    })

    return evaluationBatch
  }
}
