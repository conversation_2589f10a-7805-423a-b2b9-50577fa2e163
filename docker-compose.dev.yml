version: '3.8'

services:
  # Databases
  mysql-account:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: account
    ports:
      - "3306:3306"
    volumes:
      - mysql-account-data:/var/lib/mysql

  mysql-person:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: person
    ports:
      - "3307:3306"
    volumes:
      - mysql-person-data:/var/lib/mysql

  mysql-wallet:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: wallet
    ports:
      - "3309:3306"
    volumes:
      - mysql-wallet-data:/var/lib/mysql

  mysql-evaluation:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: evaluation
    ports:
      - "3311:3306"
    volumes:
      - mysql-evaluation-data:/var/lib/mysql

  # Microservices (built from local code)
  ms-account:
    build:
      context: ./ms-account
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    volumes:
      - ./ms-account:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - mysql-account
    command: npm run dev

  ms-person:
    build:
      context: ./ms-person
      dockerfile: Dockerfile.dev
    ports:
      - "8081:8081"
    volumes:
      - ./ms-person:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - mysql-person
    command: npm run dev

  ms-wallet:
    build:
      context: ./ms-wallet
      dockerfile: Dockerfile.dev
    ports:
      - "8083:8083"
    volumes:
      - ./ms-wallet:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - mysql-wallet
    command: npm run dev

  ms-evaluation:
    build:
      context: ./ms-evaluation
      dockerfile: Dockerfile.dev
    ports:
      - "8085:8085"
    volumes:
      - ./ms-evaluation:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - mysql-evaluation
    command: npm run dev

volumes:
  mysql-account-data:
  mysql-person-data:
  mysql-wallet-data:
  mysql-evaluation-data: