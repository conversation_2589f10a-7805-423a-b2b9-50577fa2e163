import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateStockDto,
  FetchStocksProps,
  StockData,
  UpdateStockDto,
} from '@app/domain/database/Stock'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class Stock {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<StockData> {
    const stock = await this.prisma.stock.findFirst({
      where: { id, deleted: false },
      select: {
        id: true,
        productId: true,
        productClassificationId: true,
        stockLocationId: true,
        notes: true,
        buyingPrice: true,
        expectedSellingPrice: true,
      },
    })

    if (!stock) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'stock' }),
      )
    }

    return {
      ...stock,
      notes: stock.notes ?? undefined,
    }
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    productId,
    productClassificationId,
    stockLocationId,
  }: FetchStocksProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(productId && { productId }),
      ...(productClassificationId && { productClassificationId }),
      ...(stockLocationId && { stockLocationId }),
      ...(search && {
        OR: [{ notes: { contains: search } }],
      }),
    }

    const [stocks, totalItems] = await this.prisma.$transaction([
      this.prisma.stock.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: {
          id: true,
          productId: true,
          productClassificationId: true,
          stockLocationId: true,
          notes: true,
          buyingPrice: true,
          expectedSellingPrice: true,
        },
      }),
      this.prisma.stock.count({ where }),
    ])

    return {
      items: stocks,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    productId,
    productClassificationId,
    stockLocationId,
    notes,
    buyingPrice,
    expectedSellingPrice,
  }: CreateStockDto): Promise<StockData> {
    const stockData = {
      productId,
      productClassificationId,
      stockLocation: {
        connect: { id: stockLocationId },
      },
      notes,
      buyingPrice,
      expectedSellingPrice,
    }

    const stock = await this.prisma.stock.create({
      data: stockData,
      select: {
        id: true,
        productId: true,
        productClassificationId: true,
        stockLocationId: true,
        notes: true,
        buyingPrice: true,
        expectedSellingPrice: true,
      },
    })

    return {
      ...stock,
      notes: stock.notes ?? undefined,
    }
  }

  public async update({
    id,
    productId,
    productClassificationId,
    stockLocationId,
    notes,
    buyingPrice,
    expectedSellingPrice,
  }: UpdateStockDto): Promise<StockData> {
    // Build the update data object with only the fields that are provided
    const updateData: Record<string, unknown> = {}

    if (productId !== undefined) updateData.productId = productId
    if (productClassificationId !== undefined)
      updateData.productClassificationId = productClassificationId
    if (notes !== undefined) updateData.notes = notes
    if (buyingPrice !== undefined) updateData.buyingPrice = buyingPrice
    if (expectedSellingPrice !== undefined)
      updateData.expectedSellingPrice = expectedSellingPrice

    // Handle stockLocationId separately since it requires a connect operation
    if (stockLocationId !== undefined) {
      updateData.stockLocation = {
        connect: { id: stockLocationId },
      }
    }

    const stock = await this.prisma.stock.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        productId: true,
        productClassificationId: true,
        stockLocationId: true,
        notes: true,
        buyingPrice: true,
        expectedSellingPrice: true,
      },
    })

    return {
      ...stock,
      notes: stock.notes ?? undefined,
    }
  }

  public async delete(id: string): Promise<StockData> {
    const stock = await this.prisma.stock.update({
      where: { id },
      data: { deleted: true },
      select: {
        id: true,
        productId: true,
        productClassificationId: true,
        stockLocationId: true,
        notes: true,
        buyingPrice: true,
        expectedSellingPrice: true,
      },
    })

    return {
      ...stock,
      notes: stock.notes ?? undefined,
    }
  }
}
