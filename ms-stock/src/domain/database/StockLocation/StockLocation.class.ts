import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateStockLocationDto,
  FetchStockLocationsProps,
  UpdateStockLocationDto,
} from '@app/domain/database/StockLocation'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class StockLocation {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string) {
    const stockLocation = await this.prisma.stockLocation.findFirst({
      where: { id, deleted: false },
      select: {
        id: true,
        description: true,
        parentStockLocationId: true,
      },
    })

    if (!stockLocation) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'stock location' }),
      )
    }

    return stockLocation
  }

  public async findByDescription(description: string) {
    return this.prisma.stockLocation.findFirst({
      where: { description, deleted: false },
      select: {
        id: true,
        description: true,
        parentStockLocationId: true,
      },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchStockLocationsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [{ description: { contains: search } }],
      }),
    }

    const [stockLocations, totalItems] = await this.prisma.$transaction([
      this.prisma.stockLocation.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: {
          id: true,
          description: true,
          parentStockLocationId: true,
        },
      }),
      this.prisma.stockLocation.count({ where }),
    ])

    return {
      items: stockLocations,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    description,
    parentStockLocationId,
  }: CreateStockLocationDto) {
    const data: {
      description: string
      parentStockLocation?: {
        connect: { id: string }
      }
    } = { description }

    if (parentStockLocationId) {
      data.parentStockLocation = {
        connect: { id: parentStockLocationId },
      }
    }

    const stockLocation = await this.prisma.stockLocation.create({
      data,
      select: {
        id: true,
        description: true,
        parentStockLocationId: true,
      },
    })

    return stockLocation
  }

  public async update({
    id,
    description,
    parentStockLocationId,
  }: UpdateStockLocationDto) {
    const data: {
      description: string
      parentStockLocation?: {
        connect?: { id: string }
        disconnect?: boolean
      }
    } = { description }

    if (parentStockLocationId) {
      data.parentStockLocation = {
        connect: { id: parentStockLocationId },
      }
    } else {
      // If parentStockLocationId is not provided, disconnect any existing parent
      data.parentStockLocation = {
        disconnect: true,
      }
    }

    const stockLocation = await this.prisma.stockLocation.update({
      where: { id },
      data,
      select: {
        id: true,
        description: true,
        parentStockLocationId: true,
      },
    })

    return stockLocation
  }

  public async delete(id: string) {
    const stockLocation = await this.prisma.stockLocation.update({
      where: { id },
      data: { deleted: true },
    })

    return stockLocation
  }
}
