import { Language } from '@app/application/Language'
import type {
  PersonCreateDto,
  PersonResponse,
  PersonUpdateDto,
} from '@app/application/Person'

import { Person as PersonDomain } from '@app/domain/database/Person'

export class Person {
  private personDomain = new PersonDomain()

  public async create({
    firstName,
    lastName,
  }: PersonCreateDto): Promise<{ id: string }> {
    const personId = await this.personDomain.create({
      firstName,
      lastName,
    })

    return { id: personId }
  }

  public async update({
    personId,
    firstName,
    lastName,
  }: PersonUpdateDto): Promise<void> {
    if (!personId) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'person.id' }),
      )
    }

    await this.personDomain.update({
      personId,
      firstName,
      lastName,
    })
  }

  public async findById(id: string): Promise<PersonResponse> {
    if (!id) {
      throw new ReferenceError(
        Language.translate('common:invalid', { name: 'person.id' }),
      )
    }

    return this.personDomain.findById(id)
  }
}
