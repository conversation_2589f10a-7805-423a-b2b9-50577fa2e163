import { z } from 'zod'

export const CreateAccountSchema = z.object({
  email: z.email().min(5).max(255),
  isActive: z.boolean().default(true),
  roleId: z.uuid(),
  firstName: z.string().min(3).optional(),
  lastName: z.string().min(3).optional(),
})

export const UpdateAccountSchema = z.object({
  id: z.uuid(),
  email: z.email().min(5).max(255).optional(),
  isActive: z.boolean().optional(),
  roleId: z.uuid().optional(),
  firstName: z.string().min(3).optional(),
  lastName: z.string().min(3).optional(),
})
