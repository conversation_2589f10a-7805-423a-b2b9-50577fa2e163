import { validateWithZod } from '@thrift/common/engines/Validation'

import type { UpdateAccountDto } from '@app/application/Account/Account'
import { UpdateAccountSchema } from '@app/application/Account/Account.schema'
import { Language } from '@app/application/Language'
import { Person } from '@app/application/Person'

import { Account } from '@app/domain/database/Account/Account.class'
import { Role } from '@app/domain/database/Role'

export class Update {
  public async update(input: unknown) {
    const dto: UpdateAccountDto = validateWithZod(UpdateAccountSchema, input)

    const { id, email, isActive, roleId, firstName, lastName } = dto

    const model = new Account()

    // Check if the account exists
    const existingAccount = await model.findById(id)

    if (!existingAccount) {
      throw new ReferenceError(
        Language.translate('common:notFound', { name: 'account' }),
      )
    }

    // Check if the email already exists (if email is being updated)
    if (email) {
      const existingEmail = await model.findByEmail(email)

      if (existingEmail && existingEmail.id !== id) {
        throw new ReferenceError(
          Language.translate('common:invalid', { name: 'account.email' }),
        )
      }
    }

    // Check if the roleId exists (if roleId is being updated)
    if (roleId) {
      const roleModel = new Role()
      const existingRole = await roleModel.findRole(roleId)

      if (!existingRole) {
        throw new ReferenceError(
          Language.translate('common:invalid', { name: 'account.roleId' }),
        )
      }
    }

    // Update person data if firstName or lastName are provided
    if (firstName !== undefined || lastName !== undefined) {
      if (!existingAccount.personId) {
        throw new ReferenceError(
          Language.translate('common:invalid', { name: 'account.personId' }),
        )
      }

      const personModel = new Person()

      await personModel.update({
        personId: existingAccount.personId,
        firstName,
        lastName,
      })
    }

    const item = await model.update({
      id,
      email,
      isActive,
      roleId,
    })

    return item
  }
}
