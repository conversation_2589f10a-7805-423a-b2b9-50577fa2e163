import type { PaginationParams } from '@thrift/common/engines/Pagination'

export type CredentialsProps = {
  email: string
  password: string
}

export type FetchAccountsProps = PaginationParams

export type CreateAccountDto = {
  email: string
  password: string
  isActive?: boolean
  roleId: string
  personId: string
}

export type UpdateAccountDto = {
  id: string
  email?: string
  isActive?: boolean
  roleId?: string
}

export type AccountWithPersonData = {
  id: string
  email: string
  isActive: boolean
  personId: string | null
  role: {
    id: string
    name: string
    shortname: string
  } | null
  firstName?: string
  lastName?: string
}
