import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'
import { compare, hashSync } from 'bcryptjs'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  AccountWithPersonData,
  CreateAccountDto,
  CredentialsProps,
  FetchAccountsProps,
  UpdateAccountDto,
} from '@app/domain/database/Account'
import { Person } from '@app/domain/database/Person/Person.class'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class Account {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  private getAccountSelect() {
    return {
      id: true,
      email: true,
      isActive: true,
      personId: true,
      role: {
        select: {
          id: true,
          name: true,
          shortname: true,
        },
      },
    }
  }

  public async findByCredentials({ email, password }: CredentialsProps) {
    const account = await this.prisma.account.findFirst({
      where: { email, deleted: false },
      include: {
        role: true,
      },
    })

    if (!account) {
      throw new ReferenceError(i18next.t('common:notFound', { name: 'email' }))
    }

    if (!(await compare(password, account.password))) {
      throw new ReferenceError(
        i18next.t('database:invalid', { name: 'password' }),
      )
    }

    return account
  }

  public async findById(id: string): Promise<AccountWithPersonData> {
    const account = await this.prisma.account.findFirst({
      where: { id, deleted: false },
      select: this.getAccountSelect(),
    })

    if (!account) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'account' }),
      )
    }

    if (account.personId) {
      try {
        const personModel = new Person()
        const personData = await personModel.findById(account.personId)

        return {
          ...account,
          firstName: personData.data.firstName,
          lastName: personData.data.lastName,
        }
      } catch {
        return account
      }
    }

    return account
  }

  public async findByEmail(email: string) {
    return this.prisma.account.findFirst({
      where: { email, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
  }: FetchAccountsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(search && {
        OR: [{ email: { contains: search } }],
      }),
    }

    const [accounts, totalItems] = await this.prisma.$transaction([
      this.prisma.account.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
        select: this.getAccountSelect(),
      }),
      this.prisma.account.count({ where }),
    ])

    return {
      items: accounts,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    email,
    password,
    isActive,
    roleId,
    personId,
  }: CreateAccountDto) {
    const account = await this.prisma.account.create({
      data: {
        email,
        password: hashSync(password, 10),
        isActive,
        roleId,
        personId,
      },
      select: this.getAccountSelect(),
    })

    return account
  }

  public async update({ id, email, isActive, roleId }: UpdateAccountDto) {
    const updateData: {
      email?: string
      isActive?: boolean
      roleId?: string
    } = {}

    if (email !== undefined) updateData.email = email
    if (isActive !== undefined) updateData.isActive = isActive
    if (roleId !== undefined) updateData.roleId = roleId

    const account = await this.prisma.account.update({
      where: { id },
      data: updateData,
      select: this.getAccountSelect(),
    })

    return account
  }

  public async delete(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { deleted: true },
      select: this.getAccountSelect(),
    })

    return account
  }

  public async deactivate(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { isActive: false },
      select: this.getAccountSelect(),
    })

    return account
  }

  public async activate(id: string) {
    const account = await this.prisma.account.update({
      where: { id },
      data: { isActive: true },
      select: this.getAccountSelect(),
    })

    return account
  }
}
