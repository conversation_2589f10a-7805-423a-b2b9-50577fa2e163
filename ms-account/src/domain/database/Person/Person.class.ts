import { personServiceClient } from '@app/config/microservices'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import { Debug } from '@thrift/common/engines/Debug'

import type {
  PersonNaturalCreateProps,
  PersonNaturalUpdateProps,
  PersonResponse,
} from '@app/domain/database/Person/Person.d'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class Person {
  private client = personServiceClient

  public async create(props: PersonNaturalCreateProps): Promise<string> {
    try {
      const naturalPersonResponse = await this.client.post<PersonResponse>(
        '/person-natural',
        {
          firstName: props.firstName,
          lastName: props.lastName,
        },
      )

      if (!naturalPersonResponse.data) {
        throw new ReferenceError(i18next.t('database:invalidData'))
      }

      return naturalPersonResponse.data.data.personId
    } catch (error) {
      Debug.error({ message: 'Error creating person', error })
      throw new ReferenceError(i18next.t('database:invalidData'))
    }
  }

  public async update(props: PersonNaturalUpdateProps): Promise<void> {
    try {
      await this.client.put(`/person-natural/${props.personId}`, {
        firstName: props.firstName,
        lastName: props.lastName,
      })
    } catch (error) {
      Debug.error({ message: 'Error updating person', error })
      throw new ReferenceError(i18next.t('database:invalidData'))
    }
  }

  public async findById(id: string): Promise<PersonResponse> {
    try {
      const naturalResponse = await this.client.get<PersonResponse>(
        `/person-natural/personId/${id}`,
      )

      if (naturalResponse.data?.data) {
        return naturalResponse.data
      }

      throw new ReferenceError(i18next.t('common:notFound', { name: 'person' }))
    } catch (error) {
      Debug.error({ message: 'Error finding person', error })
      throw new ReferenceError(i18next.t('common:notFound', { name: 'person' }))
    }
  }
}
