export type PersonNaturalModel = {
  id: string
  personId: string
  displayName: string
  firstName?: string
  lastName?: string
  createdAt: Date
  updatedAt: Date
}

export type PersonResponse = {
  data: PersonNaturalModel
}

export type PersonCreateProps = unknown

export type PersonNaturalCreateProps = {
  firstName?: string
  lastName?: string
  personId?: string
}

export type PersonNaturalUpdateProps = {
  personId: string
  firstName?: string
  lastName?: string
}
