{"name": "@thrift/account", "private": true, "version": "1.0.0", "description": "Account application service", "author": "Thrift Technology <<EMAIL>>", "type": "commonjs", "repository": {"type": "git", "url": "https://github.com/thrift-technology/ms-account.git"}, "scripts": {"build": "yarn healthcheck && tsc --project tsconfig.json && tsc-alias -p tsconfig.json", "dev": "yarn healthcheck && tsx watch src/main.ts", "format": "prettier --check .", "format:fix": "prettier --write .", "healthcheck": "yarn lint && yarn format && tsc --project tsconfig.json --noEmit", "lint": "eslint ./src --ext .ts", "lint:fix": "yarn lint --fix", "postinstall": "prisma generate", "prepare": "husky", "start": "node dist/src/main.js", "typecheck": "tsc --noEmit", "prisma:studio": "prisma studio"}, "devDependencies": {"@types/bcryptjs": "3.0.0", "@types/jsonwebtoken": "9.0.10", "@types/node": "24.1.0", "eslint": "9.32.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-prettier": "5.5.3", "eslint-plugin-unused-imports": "4.1.4", "husky": "^9.1.7", "prettier": "3.6.2", "tsc-alias": "1.8.16", "tsx": "4.20.3", "typescript": "5.8.3", "typescript-eslint": "8.38.0"}, "dependencies": {"@prisma/client": "6.13.0", "@thrift/common": "https://github.com/thrift-technology/ms-common.git#main", "@thrift/i18n": "https://github.com/thrift-technology/i18n.git#main", "bcryptjs": "3.0.2", "jsonwebtoken": "9.0.2", "prisma": "6.13.0"}, "engines": {"node": ">=20 <24", "npm": ">=10 <12", "yarn": ">=1.22 <2"}, "prisma": {"schema": "./prisma/schema"}, "packageManager": "yarn@4.9.2"}