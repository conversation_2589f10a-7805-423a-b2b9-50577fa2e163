import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreatePaymentMethodDto,
  FetchPaymentMethodsProps,
  PaymentMethodModel,
  UpdatePaymentMethodDto,
} from '@app/domain/database/PaymentMethod'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class PaymentMethod {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<PaymentMethodModel> {
    const paymentMethod = await this.prisma.paymentMethod.findFirst({
      where: { id, deleted: false },
    })

    if (!paymentMethod) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'payment method' }),
      )
    }

    return paymentMethod
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    tenantId,
    provider,
    enabled,
  }: FetchPaymentMethodsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(tenantId && { tenantId }),
      ...(provider && { provider }),
      ...(enabled !== undefined && { enabled }),
      ...(search && {
        OR: [{ provider: { contains: search } }],
      }),
    }

    const [paymentMethods, totalItems] = await this.prisma.$transaction([
      this.prisma.paymentMethod.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.paymentMethod.count({ where }),
    ])

    return {
      items: paymentMethods,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    tenantId,
    provider,
    config,
    enabled = true,
  }: CreatePaymentMethodDto): Promise<PaymentMethodModel> {
    const paymentMethod = await this.prisma.paymentMethod.create({
      data: {
        tenant: {
          connect: { id: tenantId },
        },
        provider,
        config,
        enabled,
      },
    })

    return paymentMethod
  }

  public async update({
    id,
    provider,
    config,
    enabled,
  }: UpdatePaymentMethodDto): Promise<PaymentMethodModel> {
    // First check if payment method exists
    await this.findById(id)

    // Build the update data object with only the fields that are provided
    const updateData: Record<string, unknown> = {}

    if (provider !== undefined) updateData.provider = provider
    if (config !== undefined) updateData.config = config
    if (enabled !== undefined) updateData.enabled = enabled

    const paymentMethod = await this.prisma.paymentMethod.update({
      where: { id },
      data: updateData,
    })

    return paymentMethod
  }

  public async delete(id: string): Promise<PaymentMethodModel> {
    // First check if payment method exists
    await this.findById(id)

    const paymentMethod = await this.prisma.paymentMethod.update({
      where: { id },
      data: { deleted: true },
    })

    return paymentMethod
  }
}
