import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type { TenantDocumentModel } from '@app/domain/database/TenantDocument'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class TenantDocumentBase {
  protected prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<TenantDocumentModel> {
    const tenantDocument = await this.prisma.tenantDocument.findFirst({
      where: { id, deleted: false },
    })

    if (!tenantDocument) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'tenant document' }),
      )
    }

    return tenantDocument
  }

  public async findByTypeAndValue(
    tenantId: string,
    type: string,
    value: string,
  ): Promise<TenantDocumentModel | null> {
    return this.prisma.tenantDocument.findFirst({
      where: { tenantId, type, value, deleted: false },
    })
  }
}
