import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateTenantContactDto,
  FetchTenantContactsProps,
  TenantContactModel,
  UpdateTenantContactDto,
} from '@app/domain/database/TenantContact'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class TenantContact {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<TenantContactModel> {
    const tenantContact = await this.prisma.tenantContact.findFirst({
      where: { id, deleted: false },
    })

    if (!tenantContact) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'tenant contact' }),
      )
    }

    return tenantContact
  }

  public async findByTypeAndValue(
    tenantId: string,
    type: string,
    value: string,
  ): Promise<TenantContactModel | null> {
    return this.prisma.tenantContact.findFirst({
      where: { tenantId, type, value, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    id,
    tenantId,
  }: FetchTenantContactsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(id && { id }),
      ...(tenantId && { tenantId }),
      ...(search && {
        OR: [{ type: { contains: search } }, { value: { contains: search } }],
      }),
    }

    const [tenantContacts, totalItems] = await this.prisma.$transaction([
      this.prisma.tenantContact.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.tenantContact.count({ where }),
    ])

    return {
      items: tenantContacts,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    tenantId,
    type,
    value,
    isPrimary = false,
  }: CreateTenantContactDto): Promise<TenantContactModel> {
    // Check if contact with same type and value already exists for this tenant
    const existingContact = await this.findByTypeAndValue(tenantId, type, value)

    if (existingContact) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'tenant contact',
          field: 'type and value',
        }),
      )
    }

    if (isPrimary) {
      await this.prisma.tenantContact.updateMany({
        where: { tenantId, type, isPrimary: true },
        data: { isPrimary: false },
      })
    }

    const tenantContact = await this.prisma.tenantContact.create({
      data: {
        tenant: {
          connect: { id: tenantId },
        },
        type,
        value,
        isPrimary,
      },
    })

    return tenantContact
  }

  public async update({
    id,
    type,
    value,
    isPrimary,
  }: UpdateTenantContactDto): Promise<TenantContactModel> {
    const existingContact = await this.findById(id)

    const updateData: Record<string, unknown> = {}

    if (type !== undefined && value !== undefined) {
      const contactWithSameTypeAndValue = await this.findByTypeAndValue(
        existingContact.tenantId,
        type,
        value,
      )

      if (
        contactWithSameTypeAndValue &&
        contactWithSameTypeAndValue.id !== id
      ) {
        throw new ConflictException(
          i18next.t('common:conflict', {
            entity: 'tenant contact',
            field: 'type and value',
          }),
        )
      }
      updateData.type = type
      updateData.value = value
    } else if (type !== undefined) {
      updateData.type = type
    } else if (value !== undefined) {
      updateData.value = value
    }

    if (isPrimary !== undefined) {
      updateData.isPrimary = isPrimary

      if (isPrimary) {
        await this.prisma.tenantContact.updateMany({
          where: {
            tenantId: existingContact.tenantId,
            type: type || existingContact.type,
            isPrimary: true,
            id: { not: id },
          },
          data: { isPrimary: false },
        })
      }
    }

    const tenantContact = await this.prisma.tenantContact.update({
      where: { id },
      data: updateData,
    })

    return tenantContact
  }

  public async delete(id: string): Promise<TenantContactModel> {
    const tenantContact = await this.prisma.tenantContact.update({
      where: { id },
      data: { deleted: true },
    })

    return tenantContact
  }
}
