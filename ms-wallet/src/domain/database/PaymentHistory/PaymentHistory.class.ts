import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreatePaymentHistoryDto,
  FetchPaymentHistoriesProps,
  PaymentHistoryModel,
  UpdatePaymentHistoryDto,
} from '@app/domain/database/PaymentHistory'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class PaymentHistory {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<PaymentHistoryModel> {
    const paymentHistory = await this.prisma.paymentHistory.findFirst({
      where: { id, deleted: false },
    })

    if (!paymentHistory) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'payment history' }),
      )
    }

    return paymentHistory
  }

  private buildDateFilter(fromDate?: Date, toDate?: Date) {
    if (fromDate && toDate) {
      return {
        createdAt: {
          gte: fromDate,
          lte: toDate,
        },
      }
    }

    if (fromDate) {
      return {
        createdAt: {
          gte: fromDate,
        },
      }
    }

    if (toDate) {
      return {
        createdAt: {
          lte: toDate,
        },
      }
    }

    return {}
  }

  private buildSearchFilter(search?: string) {
    if (!search) return {}

    return {
      OR: [
        { status: { contains: search } },
        { currency: { contains: search } },
        { externalId: { contains: search } },
      ],
    }
  }

  private buildWhereClause({
    search,
    tenantId,
    paymentMethodId,
    status,
    fromDate,
    toDate,
  }: Omit<
    FetchPaymentHistoriesProps,
    'page' | 'perPage' | 'sortBy' | 'sortOrder'
  >) {
    return {
      deleted: false,
      ...(tenantId && { tenantId }),
      ...(paymentMethodId && { paymentMethodId }),
      ...(status && { status }),
      ...this.buildDateFilter(fromDate, toDate),
      ...this.buildSearchFilter(search),
    }
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    tenantId,
    paymentMethodId,
    status,
    fromDate,
    toDate,
  }: FetchPaymentHistoriesProps) {
    const skip = (page - 1) * +perPage

    const where = this.buildWhereClause({
      search,
      tenantId,
      paymentMethodId,
      status,
      fromDate,
      toDate,
    })

    const [paymentHistories, totalItems] = await this.prisma.$transaction([
      this.prisma.paymentHistory.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.paymentHistory.count({ where }),
    ])

    return {
      items: paymentHistories,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    tenantId,
    paymentMethodId,
    amount,
    currency,
    status,
    externalId,
  }: CreatePaymentHistoryDto): Promise<PaymentHistoryModel> {
    const paymentHistory = await this.prisma.paymentHistory.create({
      data: {
        tenant: {
          connect: { id: tenantId },
        },
        paymentMethod: {
          connect: { id: paymentMethodId },
        },
        amount,
        currency,
        status,
        externalId,
      },
    })

    return paymentHistory
  }

  public async update({
    id,
    paymentMethodId,
    amount,
    currency,
    status,
    externalId,
  }: UpdatePaymentHistoryDto): Promise<PaymentHistoryModel> {
    await this.findById(id)

    const updateData: Record<string, unknown> = {}

    if (amount !== undefined) updateData.amount = amount
    if (currency !== undefined) updateData.currency = currency
    if (status !== undefined) updateData.status = status
    if (externalId !== undefined) updateData.externalId = externalId

    if (paymentMethodId !== undefined) {
      updateData.paymentMethod = {
        connect: { id: paymentMethodId },
      }
    }

    const paymentHistory = await this.prisma.paymentHistory.update({
      where: { id },
      data: updateData,
    })

    return paymentHistory
  }

  public async delete(id: string): Promise<PaymentHistoryModel> {
    await this.findById(id)

    const paymentHistory = await this.prisma.paymentHistory.update({
      where: { id },
      data: { deleted: true },
    })

    return paymentHistory
  }
}
