import { PrismaClient } from '@prisma/client'
import {
  type AllowedLanguages,
  LanguageFactory,
} from '@thrift/i18n/engines/Language'
import configs from 'app.config.json'

import ConflictException from '@thrift/common/engines/Resource/exceptions/ConflictException'
import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import type {
  CreateTenantDto,
  FetchTenantsProps,
  TenantModel,
  UpdateTenantDto,
} from '@app/domain/database/Tenant'

const i18next = LanguageFactory({
  fallbackLng: configs.server.language as AllowedLanguages,
})

export class Tenant {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  public async findById(id: string): Promise<TenantModel> {
    const tenant = await this.prisma.tenant.findFirst({
      where: { id, deleted: false },
    })

    if (!tenant) {
      throw new NotFoundException(
        i18next.t('common:notFound', { name: 'tenant' }),
      )
    }

    return tenant
  }

  public async findByTenantUid(tenantUid: string): Promise<TenantModel | null> {
    return this.prisma.tenant.findFirst({
      where: { tenantUid, deleted: false },
    })
  }

  public async findByName(name: string): Promise<TenantModel | null> {
    return this.prisma.tenant.findFirst({
      where: { name, deleted: false },
    })
  }

  public async findBySubdomain(subdomain: string): Promise<TenantModel | null> {
    return this.prisma.tenant.findFirst({
      where: { subdomain, deleted: false },
    })
  }

  public async find({
    page = 1,
    perPage = 10,
    search,
    sortBy = 'id',
    sortOrder = 'asc',
    id,
  }: FetchTenantsProps) {
    const skip = (page - 1) * +perPage

    const where = {
      deleted: false,
      ...(id && { id }),
      ...(search && {
        OR: [
          { name: { contains: search } },
          { tenantUid: { contains: search } },
        ],
      }),
    }

    const [tenants, totalItems] = await this.prisma.$transaction([
      this.prisma.tenant.findMany({
        where,
        skip,
        take: +perPage,
        orderBy: { [sortBy]: sortOrder },
      }),
      this.prisma.tenant.count({ where }),
    ])

    return {
      items: tenants,
      totalPages: Math.ceil(totalItems / +perPage),
    }
  }

  public async create({
    name,
    tenantUid,
    subdomain,
    balance = 0,
  }: CreateTenantDto): Promise<TenantModel> {
    const existingTenant = await this.findByTenantUid(tenantUid)

    if (existingTenant) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'tenant',
          field: 'tenantUid',
        }),
      )
    }

    const existingSubdomain = await this.findBySubdomain(subdomain)

    if (existingSubdomain) {
      throw new ConflictException(
        i18next.t('common:conflict', {
          entity: 'tenant',
          field: 'subdomain',
        }),
      )
    }

    const tenant = await this.prisma.tenant.create({
      data: {
        name,
        tenantUid,
        subdomain,
        balance,
      },
    })

    return tenant
  }

  public async update({
    id,
    name,
    tenantUid,
    subdomain,
    balance,
  }: UpdateTenantDto): Promise<TenantModel> {
    await this.findById(id)

    const updateData: Record<string, unknown> = {}

    if (name !== undefined) updateData.name = name

    if (tenantUid !== undefined) {
      const tenantWithSameUid = await this.findByTenantUid(tenantUid)

      if (tenantWithSameUid && tenantWithSameUid.id !== id) {
        throw new ConflictException(
          i18next.t('common:conflict', {
            entity: 'tenant',
            field: 'tenantUid',
          }),
        )
      }

      updateData.tenantUid = tenantUid
    }

    if (subdomain !== undefined) {
      const tenantWithSameSubdomain = await this.findBySubdomain(subdomain)

      if (tenantWithSameSubdomain && tenantWithSameSubdomain.id !== id) {
        throw new ConflictException(
          i18next.t('common:conflict', {
            entity: 'tenant',
            field: 'subdomain',
          }),
        )
      }

      updateData.subdomain = subdomain
    }

    if (balance !== undefined) updateData.balance = balance

    const tenant = await this.prisma.tenant.update({
      where: { id },
      data: updateData,
    })

    return tenant
  }

  public async delete(id: string): Promise<TenantModel> {
    await this.findById(id)

    const tenant = await this.prisma.tenant.update({
      where: { id },
      data: { deleted: true },
    })

    return tenant
  }
}
