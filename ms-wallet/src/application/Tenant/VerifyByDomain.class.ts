import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import { Tenant, type TenantModel } from '@app/domain/database/Tenant'

export class VerifyByDomain {
  public async verifyByDomainName(domainName: string): Promise<TenantModel> {
    if (!domainName) {
      throw new ReferenceError('Domain name is required')
    }

    const model = new Tenant()

    const tenant = await model.findBySubdomain(domainName)

    if (!tenant) {
      throw new NotFoundException('Tenant not found')
    }

    return tenant
  }
}
