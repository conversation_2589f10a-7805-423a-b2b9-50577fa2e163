import NotFoundException from '@thrift/common/engines/Resource/exceptions/NotFoundException'

import { Tenant } from '@app/domain/database/Tenant'

export class VerifyByName {
  public async verifyByName(name: string) {
    if (!name) {
      throw new ReferenceError('Tenant name is required')
    }

    const model = new Tenant()

    const tenant = await model.findByName(name)

    if (!tenant) {
      throw new NotFoundException('Tenant not found for the given name')
    }

    return tenant
  }
}
